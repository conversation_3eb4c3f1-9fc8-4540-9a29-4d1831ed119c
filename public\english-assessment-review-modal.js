/**
 * English Assessment Review Modal
 * Comprehensive modal for admin review of detailed English assessment responses
 * Includes transparency features and manual level override functionality
 */

(function() {
    'use strict';

    let isReviewModalInitialized = false;
    let currentReviewData = null;
    let currentUserEmail = null;
    let currentUserCompany = null;

    // Public API
    window.EnglishAssessmentReviewModal = {
        show: showAssessmentReviewModal,
        hide: hideReviewModal
    };

    /**
     * Show English assessment review modal with detailed responses
     * @param {string} userEmail - User's email
     * @param {string} userName - User's name
     * @param {string} userCompany - Company ID
     */
    async function showAssessmentReviewModal(userEmail, userName, userCompany) {
        try {
            // Show loading overlay
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Store current user info for level override functionality
            currentUserEmail = userEmail;
            currentUserCompany = userCompany;

            // Fetch detailed assessment data
            const reviewData = await fetchDetailedAssessmentData(userEmail, userCompany);
            currentReviewData = reviewData;

            if (isReviewModalInitialized) {
                await resetAndShowReviewModal(userName);
                return;
            }

            // Create modal if it doesn't exist
            await createReviewModal(userName);
            isReviewModalInitialized = true;

        } catch (error) {
            console.error('Error showing assessment review modal:', error);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            showErrorMessage('Failed to load detailed assessment data');
        }
    }

    /**
     * Fetch detailed assessment data including response transparency
     */
    async function fetchDetailedAssessmentData(userEmail, userCompany) {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            const userRef = db.collection('companies')
                             .doc(userCompany)
                             .collection('users')
                             .doc(userEmail);

            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            
            // Build comprehensive review data
            const reviewData = {
                // Basic assessment info
                userEmail: userEmail,
                name: userData.name || `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown',
                studentLevel: userData.studentLevel || userData.userRole || 'Not specified',
                englishAssessmentCompleted: userData.englishAssessmentCompleted || false,
                englishProficiencyScore: userData.englishProficiencyScore || 0,
                englishProficiencyLevel: userData.englishProficiencyLevel || 'Entry Level',
                englishAssessmentTimestamp: userData.englishAssessmentTimestamp || null,
                timeSpentOnEnglish: userData.timeSpentOnEnglish || 0,
                
                // Analysis data
                englishFeedback: userData.englishFeedback || {},
                englishStrengths: userData.englishStrengths || [],
                englishImprovements: userData.englishImprovements || [],
                courseRecommendations: userData.courseRecommendations || {},
                
                // Response transparency data
                englishAssessmentResponses: userData.englishAssessmentResponses || null,
                hasDetailedResponses: !!(userData.englishAssessmentResponses?.preliminaryQuestions || userData.englishAssessmentResponses?.essayResponse),
                
                // Manual override data
                manualScoreOverride: userData.manualScoreOverride || null,
                manualLevelOverride: userData.manualLevelOverride || null,
                overrideHistory: userData.overrideHistory || []
            };

            return reviewData;

        } catch (error) {
            console.error('Error fetching detailed assessment data:', error);
            throw error;
        }
    }

    /**
     * Create and show the review modal
     */
    async function createReviewModal(userName) {
        // Remove existing modal if any
        const existingOverlay = document.getElementById('english-review-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create modal HTML
        const modalHTML = createReviewModalHTML(userName);
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get modal elements
        const overlay = document.getElementById('english-review-overlay');
        if (!overlay) {
            throw new Error('Failed to create review modal');
        }

        // Initialize event listeners
        initializeReviewEventListeners(overlay);

        // Add CSS if not already added
        addReviewModalCSS();

        // Show modal with animation
        showReviewModal(overlay);
    }

    /**
     * Reset and show existing modal
     */
    async function resetAndShowReviewModal(userName) {
        const overlay = document.getElementById('english-review-overlay');
        if (!overlay) {
            await createReviewModal(userName);
            return;
        }

        // Update modal content
        const modalContent = overlay.querySelector('.english-review-modal-content');
        if (modalContent) {
            modalContent.innerHTML = createReviewModalContent(userName);
            initializeReviewEventListeners(overlay);
        }

        // Show modal
        showReviewModal(overlay);
    }

    /**
     * Show modal with animation
     */
    function showReviewModal(overlay) {
        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.english-review-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Hide the review modal
     */
    function hideReviewModal() {
        const overlay = document.getElementById('english-review-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            const modalContent = overlay.querySelector('.english-review-modal-content');
            if (modalContent) {
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'scale(0.95)';
            }
            
            setTimeout(() => {
                overlay.remove();
            }, 300);
        }
    }

    /**
     * Create modal HTML structure
     */
    function createReviewModalHTML(userName) {
        return `
            <div id="english-review-overlay" class="english-review-modal-overlay">
                <div class="english-review-modal-content">
                    ${createReviewModalContent(userName)}
                </div>
            </div>
        `;
    }

    /**
     * Create modal content
     */
    function createReviewModalContent(userName) {
        if (!currentReviewData) {
            return createReviewErrorContent();
        }

        const originalScore = currentReviewData.englishProficiencyScore || 0;
        const originalLevel = currentReviewData.englishProficiencyLevel || 'Entry Level';
        const hasScoreOverride = currentReviewData.manualScoreOverride;
        const hasLevelOverride = currentReviewData.manualLevelOverride;

        // Determine display values based on overrides
        const displayScore = hasScoreOverride ? currentReviewData.manualScoreOverride.newScore : originalScore;
        const displayLevel = hasScoreOverride ?
            calculateLevelFromScore(currentReviewData.manualScoreOverride.newScore) :
            (hasLevelOverride ? currentReviewData.manualLevelOverride.newLevel : originalLevel);
        
        return `
            <div class="english-review-header">
                <div class="english-review-title-container">
                    <h2 class="english-review-employee-title">${userName}</h2>
                    <h3 class="english-review-subtitle">Detailed Assessment Review</h3>
                </div>
                <div class="english-review-actions">
                    <button id="close-english-review-modal" class="english-review-close-button">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="english-review-body">
                <!-- Assessment Summary -->
                <div class="english-review-summary">
                    <div class="english-review-score-display">
                        <div class="english-review-score-value ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${displayScore}/21</div>
                        <div class="english-review-level-display">
                            <span class="english-review-level ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${displayLevel}</span>
                            ${hasScoreOverride ? '<span class="override-indicator">Score Override Applied</span>' : ''}
                            ${hasLevelOverride && !hasScoreOverride ? '<span class="override-indicator">Level Override Applied</span>' : ''}
                        </div>
                    </div>
                    <div class="english-review-score-override">
                        <label for="score-override-input">Override Score (0-21):</label>
                        <div class="score-override-controls">
                            <input
                                type="number"
                                id="score-override-input"
                                class="score-override-input"
                                min="0"
                                max="21"
                                step="1"
                                placeholder="${originalScore}"
                                value="${hasScoreOverride ? displayScore : ''}"
                            />
                            <div id="calculated-level" class="calculated-level">
                                ${hasScoreOverride ? `→ ${calculateLevelFromScore(displayScore)}` : ''}
                            </div>
                            <button id="save-score-override" class="save-override-btn">Save Score Override</button>
                            ${hasScoreOverride ? '<button id="remove-score-override" class="remove-override-btn">Remove Override</button>' : ''}
                        </div>
                        <div id="score-validation-message" class="validation-message"></div>
                    </div>
                </div>

                <!-- Response Transparency Section -->
                ${currentReviewData.hasDetailedResponses ? createResponseTransparencySection() : createNoResponsesSection()}
                
                <!-- AI Analysis Results -->
                ${createAnalysisSection()}
            </div>
        `;
    }

    /**
     * Create response transparency section
     */
    function createResponseTransparencySection() {
        const responses = currentReviewData.englishAssessmentResponses;
        if (!responses) return '';

        return `
            <div class="english-review-responses">
                <h3>Assessment Response Details</h3>

                ${responses.assessmentMetadata ? createMetadataSection(responses.assessmentMetadata) : ''}

                ${responses.preliminaryQuestions ? createPreliminaryQuestionsSection(responses.preliminaryQuestions) : ''}

                ${responses.essayResponse ? createEssayResponseSection(responses.essayResponse) : ''}
            </div>
        `;
    }

    /**
     * Create section for when no detailed responses are available
     */
    function createNoResponsesSection() {
        return `
            <div class="english-review-no-responses">
                <div class="no-responses-message">
                    <h3>Response Details Not Available</h3>
                    <p>Detailed response data is not available for this assessment. This may be because:</p>
                    <ul>
                        <li>The assessment was completed before response tracking was implemented</li>
                        <li>The assessment data was not properly saved</li>
                        <li>The user did not complete the full assessment</li>
                    </ul>
                    <p>Only the final score and AI analysis results are available for review.</p>
                </div>
            </div>
        `;
    }

    /**
     * Create AI analysis section
     */
    function createAnalysisSection() {
        const feedback = currentReviewData.englishFeedback || {};
        const strengths = currentReviewData.englishStrengths || [];
        const improvements = currentReviewData.englishImprovements || [];
        const courseRecs = currentReviewData.courseRecommendations || {};

        return `
            <div class="english-review-analysis">
                <h3>AI Analysis Results</h3>

                <div class="analysis-feedback-grid">
                    <div class="analysis-feedback-item">
                        <h4>Grammar & Structure</h4>
                        <p>${feedback.grammar || 'No feedback available'}</p>
                    </div>
                    <div class="analysis-feedback-item">
                        <h4>Vocabulary & Usage</h4>
                        <p>${feedback.vocabulary || 'No feedback available'}</p>
                    </div>
                    <div class="analysis-feedback-item">
                        <h4>Organization & Coherence</h4>
                        <p>${feedback.coherence || 'No feedback available'}</p>
                    </div>
                    <div class="analysis-feedback-item">
                        <h4>Overall Assessment</h4>
                        <p>${feedback.overall || 'No feedback available'}</p>
                    </div>
                </div>

                <div class="analysis-strengths-improvements">
                    <div class="analysis-strengths">
                        <h4>Identified Strengths</h4>
                        <ul>
                            ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="analysis-improvements">
                        <h4>Areas for Development</h4>
                        <ul>
                            ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                ${courseRecs.eligible ? `
                    <div class="analysis-course-recommendations">
                        <h4>Course Recommendations</h4>
                        <p>${courseRecs.description || ''}</p>
                        <div class="recommended-courses">
                            ${courseRecs.eligible.map(course => `<span class="course-tag">${course}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Create assessment metadata section
     */
    function createMetadataSection(metadata) {
        const startTime = metadata.startTime ? new Date(metadata.startTime.toDate ? metadata.startTime.toDate() : metadata.startTime) : null;
        const endTime = metadata.endTime ? new Date(metadata.endTime.toDate ? metadata.endTime.toDate() : metadata.endTime) : null;
        const duration = metadata.totalDuration || 0;

        return `
            <div class="assessment-metadata-section">
                <h4>Assessment Metadata</h4>
                <div class="metadata-grid">
                    <div class="metadata-item">
                        <label>Total Duration:</label>
                        <span>${Math.floor(duration / 60)}m ${duration % 60}s</span>
                    </div>
                    <div class="metadata-item">
                        <label>Started:</label>
                        <span>${startTime ? startTime.toLocaleString() : 'Not recorded'}</span>
                    </div>
                    <div class="metadata-item">
                        <label>Completed:</label>
                        <span>${endTime ? endTime.toLocaleString() : 'Not recorded'}</span>
                    </div>
                    <div class="metadata-item">
                        <label>Browser:</label>
                        <span>${metadata.browserInfo || 'Not recorded'}</span>
                    </div>
                    <div class="metadata-item">
                        <label>Screen Resolution:</label>
                        <span>${metadata.screenResolution || 'Not recorded'}</span>
                    </div>
                    <div class="metadata-item">
                        <label>Timezone:</label>
                        <span>${metadata.timezone || 'Not recorded'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create preliminary questions section
     */
    function createPreliminaryQuestionsSection(preliminaryQuestions) {
        if (!preliminaryQuestions || preliminaryQuestions.length === 0) {
            return '';
        }

        return `
            <div class="preliminary-questions-section">
                <h4>Preliminary Questions (${preliminaryQuestions.length})</h4>
                <div class="questions-list">
                    ${preliminaryQuestions.map((question, index) => createQuestionCard(question, index)).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Create individual question card
     */
    function createQuestionCard(question, index) {
        const timestamp = question.timestamp ? new Date(question.timestamp.toDate ? question.timestamp.toDate() : question.timestamp) : null;
        const isCorrect = question.studentResponse && question.correctAnswer &&
                         question.studentResponse.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();

        return `
            <div class="question-card ${isCorrect ? 'correct' : 'incorrect'}">
                <div class="question-header">
                    <div class="question-info">
                        <span class="question-number">Q${question.questionNumber || index + 1}</span>
                        <span class="question-type">${question.questionType || 'Unknown'}</span>
                        <span class="question-result ${isCorrect ? 'correct' : 'incorrect'}">
                            ${isCorrect ? '✓ Correct' : '✗ Incorrect'}
                        </span>
                    </div>
                    <div class="question-timing">
                        <span class="time-spent">${question.timeSpent || 0}s</span>
                    </div>
                </div>

                <div class="question-content">
                    <div class="question-text">
                        <strong>Question:</strong> ${question.questionText || 'Question text not available'}
                    </div>
                    ${question.questionContent ? `
                        <div class="question-context">
                            <strong>Context:</strong> "${question.questionContent}"
                        </div>
                    ` : ''}
                    <div class="question-answers">
                        <div class="expected-answer">
                            <strong>Expected Answer:</strong> "${question.correctAnswer || 'Not available'}"
                        </div>
                        <div class="student-answer">
                            <strong>Student Response:</strong> "${question.studentResponse || 'No response'}"
                        </div>
                    </div>
                </div>

                ${timestamp ? `
                    <div class="question-metadata">
                        <small>Answered at: ${timestamp.toLocaleString()}</small>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Create essay response section
     */
    function createEssayResponseSection(essayResponse) {
        if (!essayResponse) return '';

        const startTime = essayResponse.essayStartTime ? new Date(essayResponse.essayStartTime.toDate ? essayResponse.essayStartTime.toDate() : essayResponse.essayStartTime) : null;
        const submitTime = essayResponse.submissionTime ? new Date(essayResponse.submissionTime.toDate ? essayResponse.submissionTime.toDate() : essayResponse.submissionTime) : null;

        return `
            <div class="essay-response-section">
                <h4>Essay Response</h4>
                <div class="essay-stats">
                    <div class="essay-stat">
                        <label>Word Count:</label>
                        <span>${essayResponse.wordCount || 0}</span>
                    </div>
                    <div class="essay-stat">
                        <label>Character Count:</label>
                        <span>${essayResponse.characterCount || 0}</span>
                    </div>
                    <div class="essay-stat">
                        <label>Time Spent:</label>
                        <span>${Math.floor((essayResponse.timeSpent || 0) / 60)}m ${(essayResponse.timeSpent || 0) % 60}s</span>
                    </div>
                    <div class="essay-stat">
                        <label>Submission Type:</label>
                        <span class="submission-type ${essayResponse.submissionType || 'unknown'}">${essayResponse.submissionType || 'Unknown'}</span>
                    </div>
                </div>

                <div class="essay-content">
                    <div class="essay-prompt">
                        <h5>Essay Prompt</h5>
                        <div class="prompt-text">${essayResponse.prompt || 'Prompt not available'}</div>
                    </div>

                    <div class="essay-response-text">
                        <h5>Student Response</h5>
                        <div class="response-text">${essayResponse.response || 'No response provided'}</div>
                    </div>
                </div>

                <div class="essay-timing">
                    ${startTime ? `<div class="timing-info">Started: ${startTime.toLocaleString()}</div>` : ''}
                    ${submitTime ? `<div class="timing-info">Submitted: ${submitTime.toLocaleString()}</div>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Create error content when review data is not available
     */
    function createReviewErrorContent() {
        return `
            <div class="english-review-header">
                <div class="english-review-title-container">
                    <h2 class="english-review-employee-title">Assessment Review</h2>
                    <h3 class="english-review-subtitle">Data Not Available</h3>
                </div>
                <div class="english-review-actions">
                    <button id="close-english-review-modal" class="english-review-close-button">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="english-review-body">
                <div class="english-review-error-state">
                    <div class="english-review-error-icon">⚠️</div>
                    <h3>Unable to Load Assessment Review Data</h3>
                    <p>We're having trouble loading the detailed assessment information for this user.</p>
                    <button onclick="location.reload()" class="english-review-retry-button">
                        Refresh Page
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Initialize event listeners for the review modal
     */
    function initializeReviewEventListeners(overlay) {
        const closeButton = overlay.querySelector('#close-english-review-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideReviewModal);
        }

        const saveScoreOverrideButton = overlay.querySelector('#save-score-override');
        if (saveScoreOverrideButton) {
            saveScoreOverrideButton.addEventListener('click', handleScoreOverride);
        }

        const removeScoreOverrideButton = overlay.querySelector('#remove-score-override');
        if (removeScoreOverrideButton) {
            removeScoreOverrideButton.addEventListener('click', handleRemoveScoreOverride);
        }

        const scoreInput = overlay.querySelector('#score-override-input');
        if (scoreInput) {
            scoreInput.addEventListener('input', handleScoreInputChange);
            scoreInput.addEventListener('blur', handleScoreInputBlur);
        }

        overlay.addEventListener('click', reviewOverlayClickHandler);
    }

    /**
     * Handle overlay click to close modal
     */
    function reviewOverlayClickHandler(event) {
        if (event.target.id === 'english-review-overlay') {
            hideReviewModal();
        }
    }

    /**
     * Handle score input change for real-time level calculation
     */
    function handleScoreInputChange(event) {
        const score = event.target.value;
        const calculatedLevelDiv = document.getElementById('calculated-level');
        const validationDiv = document.getElementById('score-validation-message');

        if (score === '') {
            calculatedLevelDiv.textContent = '';
            validationDiv.textContent = '';
            return;
        }

        const validation = validateScore(score);
        if (validation.isValid) {
            const level = calculateLevelFromScore(score);
            calculatedLevelDiv.textContent = `→ ${level}`;
            calculatedLevelDiv.className = 'calculated-level valid';
            validationDiv.textContent = '';
        } else {
            calculatedLevelDiv.textContent = '';
            calculatedLevelDiv.className = 'calculated-level invalid';
            validationDiv.textContent = validation.message;
            validationDiv.className = 'validation-message error';
        }
    }

    /**
     * Handle score input blur for validation
     */
    function handleScoreInputBlur(event) {
        const score = event.target.value;
        if (score !== '') {
            const validation = validateScore(score);
            if (validation.isValid) {
                event.target.value = parseInt(score); // Ensure integer value
            }
        }
    }

    /**
     * Handle score override save
     */
    async function handleScoreOverride() {
        try {
            const scoreInput = document.getElementById('score-override-input');
            if (!scoreInput) return;

            const newScore = scoreInput.value;
            const saveButton = document.getElementById('save-score-override');

            // Validate score
            const validation = validateScore(newScore);
            if (!validation.isValid) {
                showErrorMessage(validation.message);
                return;
            }

            // Show loading state
            if (saveButton) {
                saveButton.disabled = true;
                saveButton.textContent = 'Saving...';
            }

            // Apply score override
            await applyScoreOverride(parseInt(newScore));

            // Show success message
            showSuccessMessage('Score override saved successfully');

            // Refresh the modal content
            await resetAndShowReviewModal(currentReviewData.name);

        } catch (error) {
            console.error('Error saving score override:', error);
            showErrorMessage('Failed to save score override');
        } finally {
            const saveButton = document.getElementById('save-score-override');
            if (saveButton) {
                saveButton.disabled = false;
                saveButton.textContent = 'Save Score Override';
            }
        }
    }

    /**
     * Handle remove score override
     */
    async function handleRemoveScoreOverride() {
        try {
            const removeButton = document.getElementById('remove-score-override');

            // Show loading state
            if (removeButton) {
                removeButton.disabled = true;
                removeButton.textContent = 'Removing...';
            }

            // Remove score override
            await removeScoreOverride();

            // Show success message
            showSuccessMessage('Score override removed successfully');

            // Refresh the modal content
            await resetAndShowReviewModal(currentReviewData.name);

        } catch (error) {
            console.error('Error removing score override:', error);
            showErrorMessage('Failed to remove score override');
        } finally {
            const removeButton = document.getElementById('remove-score-override');
            if (removeButton) {
                removeButton.disabled = false;
                removeButton.textContent = 'Remove Override';
            }
        }
    }

    /**
     * Apply score override to user document
     */
    async function applyScoreOverride(newScore) {
        if (!currentUserEmail || !currentUserCompany) {
            throw new Error('User information not available');
        }

        const userRef = db.collection('companies')
                         .doc(currentUserCompany)
                         .collection('users')
                         .doc(currentUserEmail);

        const calculatedLevel = calculateLevelFromScore(newScore);
        const now = new Date();

        // Calculate new course recommendations based on the overridden score
        const newCourseRecommendations = getUpdatedCourseRecommendations(newScore, calculatedLevel);

        // Create override data for current state
        const overrideData = {
            newScore: newScore,
            originalScore: currentReviewData.englishProficiencyScore,
            calculatedLevel: calculatedLevel,
            originalLevel: currentReviewData.englishProficiencyLevel,
            overrideTimestamp: now,
            overrideBy: 'admin',
            reason: 'Manual score override'
        };

        // Create override data for history with Firestore timestamp
        const overrideDataForHistory = {
            newScore: newScore,
            originalScore: currentReviewData.englishProficiencyScore,
            calculatedLevel: calculatedLevel,
            originalLevel: currentReviewData.englishProficiencyLevel,
            overrideTimestamp: firebase.firestore.Timestamp.fromDate(now),
            overrideBy: 'admin',
            reason: 'Manual score override'
        };

        // Update the user document with new score, level, and course recommendations
        await userRef.update({
            manualScoreOverride: {
                newScore: newScore,
                originalScore: currentReviewData.englishProficiencyScore,
                calculatedLevel: calculatedLevel,
                originalLevel: currentReviewData.englishProficiencyLevel,
                overrideTimestamp: firebase.firestore.FieldValue.serverTimestamp(),
                overrideBy: 'admin',
                reason: 'Manual score override'
            },
            // Update course recommendations to match the new level
            courseRecommendations: newCourseRecommendations,
            // Remove any existing level-only override since score override takes precedence
            manualLevelOverride: firebase.firestore.FieldValue.delete(),
            overrideHistory: firebase.firestore.FieldValue.arrayUnion(overrideDataForHistory)
        });

        // Update current review data
        currentReviewData.manualScoreOverride = overrideData;
        currentReviewData.manualLevelOverride = null; // Remove level override
        currentReviewData.courseRecommendations = newCourseRecommendations; // Update course recommendations
        if (!currentReviewData.overrideHistory) {
            currentReviewData.overrideHistory = [];
        }
        currentReviewData.overrideHistory.push(overrideData);

        // Trigger real-time UI updates across all components
        await triggerRealTimeUIUpdates(currentUserEmail, currentUserCompany, {
            score: newScore,
            level: calculatedLevel,
            courseRecommendations: newCourseRecommendations,
            isOverride: true
        });
    }

    /**
     * Remove score override from user document
     */
    async function removeScoreOverride() {
        if (!currentUserEmail || !currentUserCompany) {
            throw new Error('User information not available');
        }

        const userRef = db.collection('companies')
                         .doc(currentUserCompany)
                         .collection('users')
                         .doc(currentUserEmail);

        // Restore original course recommendations based on original score and level
        const originalCourseRecommendations = getUpdatedCourseRecommendations(
            currentReviewData.englishProficiencyScore,
            currentReviewData.englishProficiencyLevel
        );

        // Remove the score override and restore original course recommendations
        await userRef.update({
            manualScoreOverride: firebase.firestore.FieldValue.delete(),
            courseRecommendations: originalCourseRecommendations
        });

        // Update current review data
        currentReviewData.manualScoreOverride = null;
        currentReviewData.courseRecommendations = originalCourseRecommendations;

        // Trigger real-time UI updates to restore original values
        await triggerRealTimeUIUpdates(currentUserEmail, currentUserCompany, {
            score: currentReviewData.englishProficiencyScore,
            level: currentReviewData.englishProficiencyLevel,
            courseRecommendations: originalCourseRecommendations,
            isOverride: false
        });
    }

    /**
     * Remove level override from user document
     */
    async function removeLevelOverride() {
        if (!currentUserEmail || !currentUserCompany) {
            throw new Error('User information not available');
        }

        const userRef = db.collection('companies')
                         .doc(currentUserCompany)
                         .collection('users')
                         .doc(currentUserEmail);

        // Remove the override
        await userRef.update({
            manualLevelOverride: firebase.firestore.FieldValue.delete()
        });

        // Update current review data
        currentReviewData.manualLevelOverride = null;
    }

    /**
     * Show success message
     */
    function showSuccessMessage(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'success');
        } else {
            alert(message);
        }
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert(message);
        }
    }

    /**
     * Add CSS styles for the review modal
     */
    function addReviewModalCSS() {
        // Check if styles already added
        if (document.getElementById('english-review-modal-styles')) {
            return;
        }

        const styleSheet = document.createElement('style');
        styleSheet.id = 'english-review-modal-styles';
        styleSheet.textContent = `
            /* English Assessment Review Modal Styles */
            .english-review-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.75);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
                padding: 1rem;
            }

            .english-review-modal-content {
                background: white;
                border-radius: 12px;
                width: 95%;
                max-width: 1200px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.3s ease;
            }

            .english-review-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1.5rem;
                border-radius: 12px 12px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .english-review-employee-title {
                font-size: 1.5rem;
                font-weight: 700;
                margin: 0;
            }

            .english-review-subtitle {
                font-size: 1rem;
                font-weight: 400;
                margin: 0.25rem 0 0 0;
                opacity: 0.9;
            }

            .english-review-close-button {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 6px;
                color: white;
                cursor: pointer;
                padding: 0.5rem;
                transition: background 0.2s ease;
            }

            .english-review-close-button:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .english-review-body {
                padding: 1.5rem;
            }

            /* Summary Section */
            .english-review-summary {
                background: #f8fafc;
                border-radius: 8px;
                padding: 1.5rem;
                margin-bottom: 1.5rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .english-review-score-display {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .english-review-score-value {
                font-size: 2rem;
                font-weight: 700;
                color: #1e40af;
            }

            .english-review-level-display {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .english-review-level {
                font-size: 1.1rem;
                font-weight: 600;
                color: #374151;
            }

            .english-review-level.overridden {
                color: #dc2626;
            }

            .override-indicator {
                font-size: 0.75rem;
                color: #dc2626;
                font-weight: 500;
            }

            .english-review-level-override {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                flex-wrap: wrap;
            }

            .level-override-select {
                padding: 0.5rem;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 0.9rem;
            }

            .save-override-btn {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .save-override-btn:hover {
                background: #2563eb;
            }

            .save-override-btn:disabled {
                background: #9ca3af;
                cursor: not-allowed;
            }

            /* Score Override Controls */
            .english-review-score-override {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                flex-wrap: wrap;
            }

            .score-override-controls {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                flex-wrap: wrap;
            }

            .score-override-input {
                padding: 0.5rem;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 0.9rem;
                width: 80px;
                text-align: center;
            }

            .score-override-input:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            }

            .calculated-level {
                font-size: 0.85rem;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                min-width: 80px;
            }

            .calculated-level.valid {
                background: #dcfce7;
                color: #166534;
            }

            .calculated-level.invalid {
                background: #fef2f2;
                color: #dc2626;
            }

            .remove-override-btn {
                background: #ef4444;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .remove-override-btn:hover {
                background: #dc2626;
            }

            .remove-override-btn:disabled {
                background: #9ca3af;
                cursor: not-allowed;
            }

            .validation-message {
                font-size: 0.8rem;
                margin-top: 0.25rem;
            }

            .validation-message.error {
                color: #dc2626;
            }

            .english-review-score-value.overridden {
                color: #dc2626;
                position: relative;
            }

            .english-review-score-value.overridden::after {
                content: " (Override)";
                font-size: 0.7rem;
                color: #dc2626;
                font-weight: normal;
            }

            /* Response Sections */
            .english-review-responses h3,
            .english-review-analysis h3 {
                color: #1f2937;
                font-size: 1.2rem;
                font-weight: 600;
                margin-bottom: 1rem;
                border-bottom: 2px solid #e5e7eb;
                padding-bottom: 0.5rem;
            }

            /* Assessment Metadata */
            .assessment-metadata-section {
                background: #f3f4f6;
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .metadata-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 0.75rem;
                margin-top: 0.75rem;
            }

            .metadata-item {
                display: flex;
                justify-content: space-between;
                padding: 0.5rem;
                background: white;
                border-radius: 4px;
                font-size: 0.85rem;
            }

            .metadata-item label {
                font-weight: 600;
                color: #374151;
            }

            /* Question Cards */
            .questions-list {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .question-card {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 1rem;
                background: white;
            }

            .question-card.correct {
                border-left: 4px solid #10b981;
            }

            .question-card.incorrect {
                border-left: 4px solid #ef4444;
            }

            .question-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.75rem;
            }

            .question-info {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .question-number {
                background: #3b82f6;
                color: white;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 600;
            }

            .question-type {
                background: #f3f4f6;
                color: #374151;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.8rem;
                text-transform: capitalize;
            }

            .question-result.correct {
                color: #10b981;
                font-weight: 600;
                font-size: 0.85rem;
            }

            .question-result.incorrect {
                color: #ef4444;
                font-weight: 600;
                font-size: 0.85rem;
            }

            .time-spent {
                background: #fbbf24;
                color: #92400e;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 600;
            }

            .question-content {
                margin-bottom: 0.75rem;
            }

            .question-text,
            .question-context {
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .question-answers {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.75rem;
                margin-top: 0.75rem;
            }

            .expected-answer,
            .student-answer {
                padding: 0.5rem;
                border-radius: 4px;
                font-size: 0.85rem;
            }

            .expected-answer {
                background: #ecfdf5;
                border: 1px solid #d1fae5;
            }

            .student-answer {
                background: #fef2f2;
                border: 1px solid #fecaca;
            }

            .question-metadata {
                font-size: 0.75rem;
                color: #6b7280;
                margin-top: 0.5rem;
                padding-top: 0.5rem;
                border-top: 1px solid #f3f4f6;
            }

            /* Essay Response */
            .essay-response-section {
                background: #f9fafb;
                border-radius: 8px;
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .essay-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .essay-stat {
                display: flex;
                justify-content: space-between;
                padding: 0.5rem;
                background: white;
                border-radius: 4px;
                font-size: 0.85rem;
            }

            .essay-stat label {
                font-weight: 600;
                color: #374151;
            }

            .submission-type.manual {
                color: #10b981;
                font-weight: 600;
            }

            .submission-type.timeout {
                color: #f59e0b;
                font-weight: 600;
            }

            .essay-content {
                margin-bottom: 1rem;
            }

            .essay-prompt,
            .essay-response-text {
                margin-bottom: 1rem;
            }

            .essay-prompt h5,
            .essay-response-text h5 {
                margin: 0 0 0.5rem 0;
                font-weight: 600;
                color: #374151;
            }

            .prompt-text,
            .response-text {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                font-size: 0.9rem;
                line-height: 1.6;
                white-space: pre-wrap;
            }

            .essay-timing {
                display: flex;
                gap: 1rem;
                font-size: 0.8rem;
                color: #6b7280;
            }

            /* Analysis Section */
            .analysis-feedback-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .analysis-feedback-item {
                background: #f8fafc;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid #3b82f6;
            }

            .analysis-feedback-item h4 {
                margin: 0 0 0.5rem 0;
                font-size: 0.9rem;
                font-weight: 600;
                color: #1f2937;
            }

            .analysis-feedback-item p {
                margin: 0;
                font-size: 0.85rem;
                line-height: 1.4;
                color: #374151;
            }

            .analysis-strengths-improvements {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .analysis-strengths,
            .analysis-improvements {
                background: #f9fafb;
                border-radius: 6px;
                padding: 1rem;
            }

            .analysis-strengths {
                border-left: 4px solid #10b981;
            }

            .analysis-improvements {
                border-left: 4px solid #f59e0b;
            }

            .analysis-strengths h4,
            .analysis-improvements h4 {
                margin: 0 0 0.75rem 0;
                font-size: 0.9rem;
                font-weight: 600;
                color: #1f2937;
            }

            .analysis-strengths ul,
            .analysis-improvements ul {
                margin: 0;
                padding-left: 1rem;
                list-style-type: disc;
            }

            .analysis-strengths li,
            .analysis-improvements li {
                font-size: 0.85rem;
                line-height: 1.4;
                color: #374151;
                margin-bottom: 0.25rem;
            }

            .analysis-course-recommendations {
                background: #f0f9ff;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid #0ea5e9;
            }

            .analysis-course-recommendations h4 {
                margin: 0 0 0.75rem 0;
                font-size: 0.9rem;
                font-weight: 600;
                color: #1f2937;
            }

            .recommended-courses {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-top: 0.75rem;
            }

            .course-tag {
                background: #dbeafe;
                color: #1e40af;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 500;
            }

            /* No Responses Section */
            .english-review-no-responses {
                background: #fef3c7;
                border: 1px solid #fbbf24;
                border-radius: 8px;
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .no-responses-message h3 {
                color: #92400e;
                margin-top: 0;
            }

            .no-responses-message p,
            .no-responses-message li {
                color: #78350f;
                font-size: 0.9rem;
                line-height: 1.5;
            }

            /* Error State */
            .english-review-error-state {
                text-align: center;
                padding: 2rem;
                color: #6b7280;
            }

            .english-review-error-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
            }

            .english-review-retry-button {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 6px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 1rem;
            }

            .english-review-retry-button:hover {
                background: #2563eb;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .english-review-modal-content {
                    width: 98%;
                    max-height: 95vh;
                }

                .english-review-summary {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .question-answers {
                    grid-template-columns: 1fr;
                }

                .analysis-strengths-improvements {
                    grid-template-columns: 1fr;
                }

                .analysis-feedback-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;
        document.head.appendChild(styleSheet);
    }

    /**
     * Calculate English proficiency level from score using the scoring rubric
     * @param {number} score - Assessment score (0-21)
     * @returns {string} - Corresponding proficiency level
     */
    function calculateLevelFromScore(score) {
        const numScore = parseInt(score);

        if (isNaN(numScore) || numScore < 0 || numScore > 21) {
            return 'Entry Level'; // Default fallback
        }

        if (numScore >= 16) {
            return 'L2/GCSE';
        } else if (numScore >= 10) {
            return 'L1';
        } else {
            return 'Entry Level'; // 9 points or below
        }
    }

    /**
     * Validate score input
     * @param {string|number} score - Score to validate
     * @returns {object} - Validation result with isValid and message
     */
    function validateScore(score) {
        const numScore = parseInt(score);

        if (isNaN(numScore)) {
            return {
                isValid: false,
                message: 'Score must be a valid number'
            };
        }

        if (numScore < 0) {
            return {
                isValid: false,
                message: 'Score cannot be less than 0'
            };
        }

        if (numScore > 21) {
            return {
                isValid: false,
                message: 'Score cannot be greater than 21'
            };
        }

        return {
            isValid: true,
            message: 'Valid score'
        };
    }

    /**
     * Get updated course recommendations based on new score and level
     * @param {number} score - New assessment score
     * @param {string} level - Calculated level from score
     * @returns {object} - Updated course recommendations
     */
    function getUpdatedCourseRecommendations(score, level) {
        // Entry Level (0-9 points)
        if (score <= 9 || level === 'Entry Level') {
            return {
                eligible: [
                    'Beginners Course',
                    'Beginners Plus Course',
                    'Entry Level Health & Social Care Courses'
                ],
                description: 'You are at Entry Level in English proficiency.',
                nextSteps: 'This learner is at Entry Level and can enroll in Beginners or Beginners Plus courses and Entry Level Health & Social Care courses to build foundational skills.'
            };
        }

        // L1 Level (10-15 points)
        if (score >= 10 && score <= 15 || level === 'L1') {
            return {
                eligible: [
                    'Level 2 Health and Social Care Course',
                    'Level 2 Digital Skills Course (and below)',
                    'Level 2 English Course',
                    'Level 1 Digital Skills Courses'
                ],
                description: 'You have achieved Level 1 English proficiency. You have access to a good range of Level 2 courses.',
                nextSteps: 'Consider taking Level 2 courses to advance your skills, or take a Level 2 English course to work toward Level 2 English proficiency.'
            };
        }

        // L2/GCSE Level (16-21 points)
        if (score >= 16 || level === 'L2/GCSE') {
            return {
                eligible: [
                    'Level 3 Digital Skills Course',
                    'Level 3 Health and Social Care Course',
                    'Advanced Digital Skills',
                    'Professional Communication'
                ],
                description: 'Excellent! You have achieved Level 2 in English proficiency.',
                nextSteps: 'You are eligible for Level 3 Digital Skills Course or below and Level 3 Health and Social Care course. You can proceed directly to advanced level courses that match your career goals.'
            };
        }

        // Default fallback
        return {
            eligible: ['Beginners Course', 'Beginners Plus Course'],
            description: 'Course recommendations will be updated based on your assessment results.',
            nextSteps: 'Please complete your assessment for personalized course recommendations.'
        };
    }

    /**
     * Trigger real-time UI updates across all application components
     * @param {string} userEmail - User's email
     * @param {string} userCompany - Company ID
     * @param {object} updateData - Updated assessment data
     */
    async function triggerRealTimeUIUpdates(userEmail, userCompany, updateData) {
        try {
            console.log('Triggering real-time UI updates for:', userEmail, updateData);

            // 1. Update English Results Modal if open
            await updateEnglishResultsModal(userEmail, updateData);

            // 2. Update assessment results table badges
            await updateAssessmentTableBadges(userEmail, updateData);

            // 3. Update dashboard displays if present
            await updateDashboardDisplays(userEmail, updateData);

            // 4. Trigger custom events for other components
            triggerCustomUpdateEvents(userEmail, updateData);

            console.log('✅ Real-time UI updates completed successfully');

        } catch (error) {
            console.error('Error during real-time UI updates:', error);
        }
    }

    /**
     * Update English Results Modal if currently open
     */
    async function updateEnglishResultsModal(userEmail, updateData) {
        try {
            // Check if English Results Modal is open and for the same user
            const englishModal = document.getElementById('english-results-overlay');
            if (!englishModal) return;

            // Check if it's for the same user (basic check)
            const modalUserEmail = englishModal.getAttribute('data-user-email');
            if (modalUserEmail && modalUserEmail !== userEmail) return;

            console.log('Updating English Results Modal...');

            // Update score display
            const scoreValue = englishModal.querySelector('.english-score-value');
            if (scoreValue) {
                scoreValue.textContent = `${updateData.score}/21`;
                scoreValue.className = updateData.isOverride ?
                    'english-score-value overridden' : 'english-score-value';
            }

            // Update level display
            const levelDisplay = englishModal.querySelector('.english-score-level');
            if (levelDisplay) {
                levelDisplay.textContent = `${updateData.level} Level`;
                levelDisplay.className = updateData.isOverride ?
                    'english-score-level overridden' : 'english-score-level';
            }

            // Update override indicator
            const existingIndicator = englishModal.querySelector('.override-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            if (updateData.isOverride) {
                const scoreDisplay = englishModal.querySelector('.english-score-display');
                if (scoreDisplay) {
                    const indicator = document.createElement('div');
                    indicator.className = 'override-indicator';
                    indicator.textContent = 'Score Override Applied';
                    scoreDisplay.appendChild(indicator);
                }
            }

            // Update qualification status
            const statusElement = englishModal.querySelector('.english-score-status');
            if (statusElement) {
                const isQualified = updateData.score >= 16;
                statusElement.textContent = isQualified ?
                    'Qualified for Digital Skills Training' :
                    'Additional English Support Recommended';
                statusElement.className = `english-score-status ${isQualified ? 'qualified' : 'needs-improvement'}`;
            }

            // Update course recommendations section
            await updateModalCourseRecommendations(englishModal, updateData.courseRecommendations);

            console.log('✅ English Results Modal updated');

        } catch (error) {
            console.error('Error updating English Results Modal:', error);
        }
    }

    /**
     * Update course recommendations section in modal
     */
    async function updateModalCourseRecommendations(modal, courseRecommendations) {
        try {
            // Find course recommendations section
            const courseSection = modal.querySelector('.english-course-recommendations');
            if (!courseSection || !courseRecommendations) return;

            // Update description
            const descriptionElement = courseSection.querySelector('.course-description');
            if (descriptionElement) {
                descriptionElement.textContent = courseRecommendations.description;
            }

            // Update next steps
            const nextStepsElement = courseSection.querySelector('.course-next-steps');
            if (nextStepsElement) {
                nextStepsElement.textContent = courseRecommendations.nextSteps;
            }

            // Update eligible courses list
            const coursesContainer = courseSection.querySelector('.eligible-courses');
            if (coursesContainer && courseRecommendations.eligible) {
                coursesContainer.innerHTML = courseRecommendations.eligible
                    .map(course => `
                        <div class="english-course-item">
                            <span class="english-course-icon">📚</span>
                            <span class="english-course-name">${course}</span>
                        </div>
                    `).join('');
            }

            console.log('✅ Course recommendations updated in modal');

        } catch (error) {
            console.error('Error updating course recommendations in modal:', error);
        }
    }

    /**
     * Update assessment results table badges
     */
    async function updateAssessmentTableBadges(userEmail, updateData) {
        try {
            console.log('Updating assessment table badges...');

            // Find all English badges for this user
            const englishBadges = document.querySelectorAll(`.english-badge-clickable[data-user-email="${userEmail}"]`);

            englishBadges.forEach(badge => {
                // Get the badge configuration for the new level
                const badgeConfig = getEnglishProficiencyBadgeConfig(updateData.score, updateData.level, updateData.isOverride);

                if (badgeConfig) {
                    // Update badge appearance
                    badge.className = `${badgeConfig.color} px-1.5 py-1 text-xs rounded-full font-medium english-badge-clickable hover:opacity-80 hover:shadow-md transition-all duration-200 border border-transparent hover:border-current inline-flex items-center gap-1`;

                    // Update badge text
                    const textSpan = badge.querySelector('span.truncate');
                    if (textSpan) {
                        textSpan.textContent = badgeConfig.level;
                    }

                    // Update tooltip
                    const tooltipText = `${badgeConfig.description} (${updateData.score} points)${updateData.isOverride ? ' - Override Applied' : ''}`;
                    badge.title = `Click to view detailed English assessment results - ${tooltipText}`;

                    // Add override indicator if needed
                    if (updateData.isOverride) {
                        badge.style.position = 'relative';
                        if (!badge.querySelector('.override-dot')) {
                            const overrideDot = document.createElement('span');
                            overrideDot.className = 'override-dot';
                            overrideDot.style.cssText = `
                                position: absolute;
                                top: -2px;
                                right: -2px;
                                width: 6px;
                                height: 6px;
                                background: #dc2626;
                                border-radius: 50%;
                                border: 1px solid white;
                            `;
                            badge.appendChild(overrideDot);
                        }
                    } else {
                        // Remove override indicator
                        const overrideDot = badge.querySelector('.override-dot');
                        if (overrideDot) {
                            overrideDot.remove();
                        }
                    }
                }
            });

            console.log('✅ Assessment table badges updated');

        } catch (error) {
            console.error('Error updating assessment table badges:', error);
        }
    }

    /**
     * Get badge configuration for English proficiency level
     */
    function getEnglishProficiencyBadgeConfig(score, level, isOverride = false) {
        const baseConfig = {
            'L2/GCSE': {
                level: 'Advanced',
                color: 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400',
                description: 'Level 2/GCSE English'
            },
            'L1': {
                level: 'Intermediate',
                color: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400',
                description: 'Level 1 English'
            },
            'Entry Level': {
                level: 'Foundation',
                color: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400',
                description: 'Entry Level English'
            }
        };

        const config = baseConfig[level] || baseConfig['Entry Level'];

        if (isOverride) {
            // Add visual indication for overrides
            config.color = config.color.replace('bg-', 'bg-red-50 border border-red-200 ');
            config.description += ' (Override Applied)';
        }

        return config;
    }

    /**
     * Update dashboard displays if present
     */
    async function updateDashboardDisplays(userEmail, updateData) {
        try {
            // This function can be extended to update dashboard-specific displays
            // For now, it uses the same badge update logic as the assessment table
            console.log('Updating dashboard displays...');

            // Dashboard uses the same badge structure as assessments table
            await updateAssessmentTableBadges(userEmail, updateData);

            console.log('✅ Dashboard displays updated');

        } catch (error) {
            console.error('Error updating dashboard displays:', error);
        }
    }

    /**
     * Trigger custom events for other components to listen to
     */
    function triggerCustomUpdateEvents(userEmail, updateData) {
        try {
            // Dispatch custom event for other components
            const event = new CustomEvent('englishAssessmentUpdated', {
                detail: {
                    userEmail,
                    updateData,
                    timestamp: new Date()
                }
            });

            document.dispatchEvent(event);

            // Also trigger on window for broader compatibility
            window.dispatchEvent(new CustomEvent('englishAssessmentOverrideApplied', {
                detail: { userEmail, updateData }
            }));

            console.log('✅ Custom update events triggered');

        } catch (error) {
            console.error('Error triggering custom events:', error);
        }
    }

    // Expose functions globally for use in other modules
    window.calculateLevelFromScore = calculateLevelFromScore;
    window.validateScore = validateScore;
    window.getUpdatedCourseRecommendations = getUpdatedCourseRecommendations;
    window.triggerRealTimeUIUpdates = triggerRealTimeUIUpdates;

})();
