<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard</title>

  <!-- Firebase scripts (if needed) -->
  <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>

  <!-- Tailwind & other styles/scripts -->
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="border-animation.css">
  <link rel="stylesheet" href="stripe-styles.css">
  <link rel="stylesheet" href="user-menu.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/litepicker/dist/css/litepicker.css">
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

  <!-- Additional scripts (html2pdf, etc.) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/litepicker/dist/litepicker.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-matrix@1.0.0"></script>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>
</head>

<body class="bg-gray-100 dark:bg-gray-950">

  <!-- Header / Navbar -->
  <header class="bg-white dark:bg-gray-900 shadow fixed top-0 left-0 right-0 z-50">
    <div class="container mx-auto flex items-center justify-between h-16 px-4 md:px-6">
      <div class="flex items-center gap-4">
        <button id="open-drawer" class="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none" title="Open Navigation Drawer">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
        <a href="#"><img src="logo.png" alt="Barefoot elearning" id="logo-image" class="h-6"></a>
      </div>

      <!-- Main Nav Links -->
      <nav class="hidden md:flex space-x-4">
        <a href="#" class="nav-link" data-content="dashboard">
          <img src="home.png" alt="Dashboard Icon">Dashboard
        </a>
        <a href="#" class="nav-link" data-content="assessments">
          <img src="audit.png" alt="Assessments Icon">Assessments
        </a>
        <a href="#" class="nav-link" data-content="learning-paths">
          <img src="online-learning.png" alt="Learning Paths Icon">Learning Paths
        </a>
        <a href="#" class="nav-link" data-content="reports">
          <img src="reports.png" alt="Metrics Icon">Metrics
        </a>
        <a href="#" class="nav-link" data-content="invite">
          <img src="invitation.png" alt="Invitations Icon">Invitations
        </a>
      </nav>

      <!-- Right Side Items -->
      <div class="flex items-center gap-4">
        <!-- Notifications (hidden) -->
        <button class="rounded-full p-2" title="Notifications" style="display: none;">
          <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 8a6 6 0 0112 0c0 7 3 9 3 9H3s3-2 3-9z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.3 21a1.94 1.94 0 003.4 0"/>
          </svg>
        </button>

        <!-- User Menu Button -->
        <div class="relative">
          <button id="user-menu-button" class="rounded-full p-2" title="Toggle User Menu">
            <img id="user-menu-avatar" alt="Avatar" class="rounded-full" src="profile.png" width="32" height="32">
          </button>

          <!-- User Menu Backdrop -->
          <div id="user-menu-backdrop" class="hidden"></div>

          <!-- User Menu Sidebar -->
          <div id="user-menu" class="hidden">
            <!-- Close Button -->
            <div id="user-menu-close">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </div>

            <!-- User Profile Section -->
            <div class="px-4 py-2">
              <div class="flex items-center">
                <div class="avatar-container">
                  <img src="profile.png" alt="User Avatar" class="rounded-full">
                </div>
                <div>
                  <p class="font-semibold">John Doe</p>
                  <p class="text-xs">Acme Inc</p>
                  <div class="inline-flex items-center mt-0">
                    <span class="inline-block w-2 h-2 rounded-full bg-green-500"></span>
                    <span class="text-[10px] text-gray-500 ml-1">Active</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Edit Profile Option -->
            <a href="#" id="edit-profile" class="menu-item">
              <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Profile
            </a>

            <!-- Demo Mode Section -->
            <div class="demo-section border-t border-gray-200">
              <div class="flex items-center justify-between mb-1">
                <span id="mode-status" class="text-xs font-medium">Live Mode</span>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" id="demo-toggle" class="sr-only">
                  <div id="demo-toggle-switch" class="bg-gray-200 rounded-full">
                    <div class="dot bg-white rounded-full shadow-md"></div>
                  </div>
                </label>
              </div>
              <p class="text-[10px] text-gray-500 mb-1 leading-tight">
                Switch to demo mode to preview features
              </p>
            </div>

            <!-- Subscription Section -->
            <div class="subscription-section border-t border-gray-200">
              <div class="flex items-center justify-between mb-2">
                <span class="font-medium flex items-center">
                  <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Subscription:
                </span>
                <span class="font-semibold">Free Trial</span>
              </div>

              <!-- This div will be shown/hidden based on subscription status -->
              <div id="subscription-end-notice" class="text-xs text-amber-600 font-medium mt-1 mb-1 flex items-center hidden">
                <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span id="subscription-end-date" class="text-[10px] leading-tight">Ends on 5/23/2025 (30 days)</span>
              </div>

              <!-- Default management buttons -->
              <div id="default-subscription-buttons">
                <button id="manageSubscriptionBtn" class="w-full border border-blue-600 text-blue-600 hover:bg-blue-50 rounded-lg py-1 px-2 text-xs flex items-center justify-center transition-colors duration-200 mt-2">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Manage Subscription
                </button>
              </div>

              <!-- Cancellation buttons (initially hidden) - Arranged vertically -->
              <div id="cancellation-subscription-buttons" class="flex flex-col space-y-2 mt-2 hidden">
                <button id="cancelStatusBtn" class="w-full border border-amber-500 text-amber-600 bg-amber-50 rounded-lg py-1 px-1 text-xs flex items-center justify-center cursor-default">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Cancelled
                </button>
                <button id="resubscribeBtn" class="w-full bg-green-600 text-white hover:bg-green-700 rounded-lg py-1 px-1 text-xs flex items-center justify-center transition-colors duration-200">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Resubscribe
                </button>
              </div>
            </div>

            <!-- Credits Section -->
            <div class="credits-section border-t border-gray-200">
              <div class="flex items-center mb-1">
                <span class="font-medium flex items-center">
                  <svg class="w-3 h-3 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927C9.435 2.371 10.565 2.371 10.951 2.927L13.361 6.36L17.292 7.043C17.939 7.152 18.19 7.98 17.687 8.434L14.864 10.865L15.589 14.792C15.714 15.438 15.016 15.934 14.434 15.597L10.5 13.377L6.566 15.597C5.984 15.934 5.286 15.438 5.411 14.792L6.136 10.865L3.313 8.434C2.81 7.98 3.061 7.152 3.708 7.043L7.639 6.36L10.049 2.927Z"></path>
                  </svg>
                  Credits:
                </span>
                <span class="font-semibold ml-2">5</span>
              </div>
              <!-- Add Credits Button (hidden by default, shown for paid users) -->
              <div id="add-credits-button-container" class="hidden">
                <button id="addCreditsBtn" class="w-full border border-blue-600 text-blue-600 hover:bg-blue-50 rounded-lg py-1 px-2 text-xs flex items-center justify-center transition-colors duration-200 mt-2">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M12 6v6m0 0v6m0-6h6m-6 0H6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Add Credits
                </button>
              </div>
            </div>

            <!-- Logout Option -->
            <a id="user-logout" href="#" class="menu-item border-t border-gray-200">
              <img src="logout.png" alt="Logout" class="w-3 h-3 mr-1">
              Logout
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Mobile Navigation Drawer -->
  <div id="backdrop" class="fixed inset-0 z-40 hidden bg-black bg-opacity-25"></div>
  <div id="navigation-drawer" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 transform -translate-x-full transition-transform duration-300 md:hidden">
    <div class="flex items-center justify-between h-16 px-4">
      <a href="#"><img src="logo.png" alt="Barefoot elearning" class="logo"></a>
      <button id="close-drawer" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none" title="Close Drawer">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <nav class="mt-8">
      <a href="#" class="nav-link" data-content="dashboard">Dashboard</a>
      <a href="#" class="nav-link" data-content="assessments">Assessments</a>
      <a href="#" class="nav-link" data-content="learning-paths">Learning Paths</a>
      <a href="#" class="nav-link" data-content="reports">Metrics</a>
      <a href="#" class="nav-link" data-content="invite">Invitations</a>
    </nav>
  </div>

  <!-- Main Content Area -->
  <main class="flex-1 py-8 mt-16">
    <!-- IMPORTANT: replaced 'container mx-auto' with 'w-full' so the child can expand -->
    <div id="main-content" class="w-full px-4 md:px-6">
      <!-- The reports.html (and other pages) will be injected here via JS or whatever method you use -->
    </div>
  </main>

  <!-- Loading Overlay -->
  <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex flex-col justify-center items-center gap-4" style="display: none;">
    <div id="loading-animation" style="width: 45px; height: 45px;"></div>
    <div class="loading-text"></div>
  </div>

  <!-- Login Success Overlay -->
  <div id="login-success-overlay" class="hidden">
    <div class="login-success-content">
      <div id="lottie-container"></div>
      <p>All signed in!</p>
    </div>
  </div>

  <!-- Popup Overlay -->
  <div id="popupOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="popup-content bg-white p-6 rounded-lg shadow-lg max-w-3xl mx-auto mt-20 overflow-y-auto">
      <!-- Content loaded dynamically -->
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.9.6/lottie.min.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
  <script src="https://js.stripe.com/v3/"></script>
  <script src="stripe-config.js"></script>
  <script src="stripe-price-config.js"></script>

  <!-- Custom scripts -->
  <script src="pageLoader.js"></script>
  <script src="feedback.js"></script>
  <script src="courseSelection.js"></script>
  <script src="stripe-handler.js"></script>
  <script src="accountmanagement.js"></script>
  <script src="skills-gap-modal.js"></script>
  <script src="english-results-modal.js"></script>
  <script src="english-assessment-review-modal.js"></script>
  <script src="math-results-modal.js"></script>
  <script src="math-assessment-review-modal.js"></script>
  <script src="warning-modal.js"></script>
  <script src="feature-access-modal.js"></script>
  <script src="subscription-check.js"></script>
  <script src="subscription-modal.js"></script>
  <script src="topup-modal.js"></script>
  <script src="user-menu.js"></script>
  <script src="mobile-detection.js"></script>
  <script src="mobile-warning.js"></script>
  <script src="script.js"></script>
</body>
</html>
