/**
 * Mathematics Results Modal
 * Professional modal for displaying detailed mathematics assessment analysis
 * Matches skills gap modal dimensions and styling
 */

(function() {
    'use strict';

    let isModalInitialized = false;
    let currentMathData = null;

    // Public API
    window.MathResultsModal = {
        show: showMathResultsModal,
        hide: hideModal
    };

    /**
     * Show Mathematics assessment results modal
     * @param {Object} mathData - Mathematics assessment data
     * @param {string} userEmail - User's email
     * @param {string} userName - User's name
     * @param {string} userCompany - Company ID
     */
    async function showMathResultsModal(mathData, userEmail, userName, userCompany) {
        try {
            // Validate required parameters
            if (!validateModalParameters(userEmail, userName, userCompany)) {
                throw new Error('Invalid parameters provided to mathematics results modal');
            }

            // Show loading overlay immediately
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Fetch enhanced Mathematics assessment data with validation
            const enhancedData = await fetchEnhancedMathData(userEmail, userCompany);
            currentMathData = validateAndEnhanceMathData(enhancedData || mathData, userEmail, userName);

            if (isModalInitialized) {
                await resetAndShowModal(userName);
                return;
            }

            // Create modal if it doesn't exist
            await createModal(userName);
            isModalInitialized = true;

        } catch (error) {
            console.error('Error showing Mathematics results modal:', error);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            if (typeof showNotification === 'function') {
                showNotification('Failed to load mathematics assessment results', 'error');
            }

            // Show fallback modal with error state
            await showFallbackModal(userName, error.message);
        }
    }

    /**
     * Hide the modal
     */
    function hideModal() {
        const overlay = document.getElementById('math-results-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.remove();
                isModalInitialized = false;
            }, 300);
        }
    }

    /**
     * Fetch enhanced mathematics assessment data with fallbacks
     */
    async function fetchEnhancedMathData(userEmail, userCompany) {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            // Validate parameters
            if (!userEmail || !userCompany) {
                throw new Error('Missing required parameters: userEmail or userCompany');
            }

            console.log('Fetching enhanced math data for:', { userEmail, userCompany });

            const userRef = db.collection('companies')
                             .doc(userCompany)
                             .collection('users')
                             .doc(userEmail);

            const userDoc = await userRef.get();

            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            console.log('Raw user data:', userData);

            // Log specific mathematics fields for debugging
            console.log('Mathematics fields:', {
                mathAssessmentCompleted: userData.mathAssessmentCompleted,
                mathCurrentLevel: userData.mathCurrentLevel,
                mathOverallScore: userData.mathOverallScore,
                mathLevel1: userData.mathLevel1,
                mathGCSEPart1: userData.mathGCSEPart1
            });
            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();

            // Extract comprehensive mathematics assessment data following the complete schema
            const mathData = {
                // Core assessment fields
                mathAssessmentCompleted: userData.mathAssessmentCompleted || false,
                mathCurrentLevel: userData.mathCurrentLevel || null,
                mathOverallScore: userData.mathOverallScore || 0,
                mathHighestLevelCompleted: userData.mathHighestLevelCompleted || null,
                mathAssessmentTimestamp: userData.mathAssessmentTimestamp || null,
                totalTimeSpentOnMath: userData.totalTimeSpentOnMath || 0,
                updatedAt: userData.updatedAt || null,

                // Level-specific assessment data with complete structure
                mathEntryLevel: userData.mathEntryLevel || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        arithmetic: { score: 0, maxScore: 8 },
                        fractions: { score: 0, maxScore: 6 },
                        percentages: { score: 0, maxScore: 4 },
                        basicAlgebra: { score: 0, maxScore: 6 },
                        measurement: { score: 0, maxScore: 4 },
                        dataHandling: { score: 0, maxScore: 4 }
                    }
                },

                mathLevel1: userData.mathLevel1 || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        advancedArithmetic: { score: 0, maxScore: 4 },
                        fractionsDecimals: { score: 0, maxScore: 4 },
                        percentagesRatio: { score: 0, maxScore: 4 },
                        algebraicExpressions: { score: 0, maxScore: 6 },
                        geometry: { score: 0, maxScore: 4 },
                        statistics: { score: 0, maxScore: 4 }
                    }
                },

                mathGCSEPart1: userData.mathGCSEPart1 || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        numberOperations: { score: 0, maxScore: 3 },
                        algebraicManipulation: { score: 0, maxScore: 3 },
                        geometricReasoning: { score: 0, maxScore: 2 },
                        fractionalCalculations: { score: 0, maxScore: 2 }
                    }
                },

                mathGCSEPart2: userData.mathGCSEPart2 || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        complexCalculations: { score: 0, maxScore: 4 },
                        statisticalAnalysis: { score: 0, maxScore: 4 },
                        trigonometry: { score: 0, maxScore: 4 },
                        advancedAlgebra: { score: 0, maxScore: 4 },
                        problemSolving: { score: 0, maxScore: 4 }
                    }
                },

                // Enhanced feedback fields
                mathFeedback: userData.mathFeedback || getDefaultMathFeedback(userData.mathOverallScore || 0),
                mathStrengths: userData.mathStrengths || getDefaultMathStrengths(userData.mathOverallScore || 0),
                mathImprovements: userData.mathImprovements || getDefaultMathImprovements(userData.mathOverallScore || 0),
                mathPlacementRecommendation: userData.mathPlacementRecommendation || getDefaultPlacementRecommendation(userData.mathOverallScore || 0, userData.mathHighestLevelCompleted),

                // Course recommendations
                courseRecommendations: userData.mathCourseRecommendations || getDefaultMathCourseRecommendations(userData.mathOverallScore || 0, userData.mathCurrentLevel || 'Entry'),

                // Manual override tracking
                manualScoreOverride: userData.manualScoreOverride || null,
                manualLevelOverride: userData.manualLevelOverride || null,
                overrideHistory: userData.overrideHistory || [],

                // User identification
                userEmail: userEmail,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                userType: userData.userType || 'student',
                studentLevel: userData.studentLevel || 'adult-learner'
            };

            return mathData;

        } catch (error) {
            console.error('Error fetching enhanced Mathematics data:', error);
            return null;
        }
    }

    /**
     * Validate modal parameters
     */
    function validateModalParameters(userEmail, userName, userCompany) {
        if (!userEmail || typeof userEmail !== 'string' || !userEmail.includes('@')) {
            console.error('Invalid userEmail provided:', userEmail);
            return false;
        }

        if (!userName || typeof userName !== 'string' || userName.trim().length === 0) {
            console.error('Invalid userName provided:', userName);
            return false;
        }

        if (!userCompany || typeof userCompany !== 'string' || userCompany.trim().length === 0) {
            console.error('Invalid userCompany provided:', userCompany);
            return false;
        }

        return true;
    }

    /**
     * Validate and enhance mathematics data with fallbacks
     */
    function validateAndEnhanceMathData(mathData, userEmail, userName) {
        if (!mathData || typeof mathData !== 'object') {
            console.warn('No mathematics data provided, creating fallback data');
            return createFallbackMathData(userEmail, userName);
        }

        // Validate core fields and provide fallbacks
        const validatedData = {
            // Core assessment fields with validation
            mathAssessmentCompleted: Boolean(mathData.mathAssessmentCompleted),
            mathCurrentLevel: validateLevel(mathData.mathCurrentLevel) || 'Entry',
            mathOverallScore: validateScore(mathData.mathOverallScore) || 0,
            mathHighestLevelCompleted: validateLevel(mathData.mathHighestLevelCompleted) || null,
            mathAssessmentTimestamp: validateTimestamp(mathData.mathAssessmentTimestamp) || null,
            totalTimeSpentOnMath: validateTimeSpent(mathData.totalTimeSpentOnMath) || 0,
            updatedAt: validateTimestamp(mathData.updatedAt) || null,

            // Level-specific data with validation
            mathEntryLevel: validateLevelData(mathData.mathEntryLevel, 'Entry'),
            mathLevel1: validateLevelData(mathData.mathLevel1, 'Level1'),
            mathGCSEPart1: validateLevelData(mathData.mathGCSEPart1, 'GCSEPart1'),
            mathGCSEPart2: validateLevelData(mathData.mathGCSEPart2, 'GCSEPart2'),

            // Enhanced feedback fields with validation
            mathFeedback: validateFeedback(mathData.mathFeedback) || getDefaultMathFeedback(mathData.mathOverallScore || 0),
            mathStrengths: validateArray(mathData.mathStrengths) || getDefaultMathStrengths(mathData.mathOverallScore || 0),
            mathImprovements: validateArray(mathData.mathImprovements) || getDefaultMathImprovements(mathData.mathOverallScore || 0),
            mathPlacementRecommendation: validatePlacementRecommendation(mathData.mathPlacementRecommendation) || getDefaultPlacementRecommendation(mathData.mathOverallScore || 0, mathData.mathHighestLevelCompleted),

            // Course recommendations with validation
            courseRecommendations: validateCourseRecommendations(mathData.courseRecommendations) || getDefaultMathCourseRecommendations(mathData.mathOverallScore || 0, mathData.mathCurrentLevel || 'Entry'),

            // Manual override tracking with validation
            manualScoreOverride: validateOverride(mathData.manualScoreOverride) || null,
            manualLevelOverride: validateOverride(mathData.manualLevelOverride) || null,
            overrideHistory: validateArray(mathData.overrideHistory) || [],

            // User identification with validation
            userEmail: userEmail,
            firstName: validateString(mathData.firstName) || '',
            lastName: validateString(mathData.lastName) || '',
            userType: validateString(mathData.userType) || 'student',
            studentLevel: validateString(mathData.studentLevel) || 'adult-learner'
        };

        return validatedData;
    }

    /**
     * Create fallback mathematics data when no data is available
     */
    function createFallbackMathData(userEmail, userName) {
        return {
            mathAssessmentCompleted: false,
            mathCurrentLevel: 'Entry',
            mathOverallScore: 0,
            mathHighestLevelCompleted: null,
            mathAssessmentTimestamp: null,
            totalTimeSpentOnMath: 0,
            updatedAt: null,

            mathEntryLevel: createDefaultLevelData('Entry'),
            mathLevel1: createDefaultLevelData('Level1'),
            mathGCSEPart1: createDefaultLevelData('GCSEPart1'),
            mathGCSEPart2: createDefaultLevelData('GCSEPart2'),

            mathFeedback: getDefaultMathFeedback(0),
            mathStrengths: getDefaultMathStrengths(0),
            mathImprovements: getDefaultMathImprovements(0),
            mathPlacementRecommendation: getDefaultPlacementRecommendation(0, null),
            courseRecommendations: getDefaultMathCourseRecommendations(0, 'Entry'),

            manualScoreOverride: null,
            manualLevelOverride: null,
            overrideHistory: [],

            userEmail: userEmail,
            firstName: userName.split(' ')[0] || '',
            lastName: userName.split(' ').slice(1).join(' ') || '',
            userType: 'student',
            studentLevel: 'adult-learner'
        };
    }

    /**
     * Show fallback modal when data loading fails
     */
    async function showFallbackModal(userName, errorMessage) {
        try {
            // Create minimal fallback data
            currentMathData = createFallbackMathData('<EMAIL>', userName);

            // Create modal with error state
            const modalHTML = `
                <div id="math-results-overlay" class="math-modal-overlay">
                    <div class="math-modal-content">
                        <div class="math-modal-header">
                            <div class="math-modal-title-container">
                                <h2 class="math-modal-employee-title">${userName}</h2>
                                <h3 class="math-modal-subtitle">Mathematics Assessment Results</h3>
                            </div>
                            <div class="math-modal-actions">
                                <button id="close-math-modal" class="math-close-modal-button">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="math-modal-body">
                            <div class="math-error-content">
                                <div class="math-error-icon">
                                    <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                    </svg>
                                </div>
                                <h3>Assessment Data Unavailable</h3>
                                <p>Unable to load mathematics assessment results: ${errorMessage}</p>
                                <p>Please try again later or contact support if the problem persists.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            addModalStyles();

            const overlay = document.getElementById('math-results-overlay');
            const closeBtn = overlay.querySelector('#close-math-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideModal);
            }

            overlay.addEventListener('click', (event) => {
                if (event.target.id === 'math-results-overlay') {
                    hideModal();
                }
            });

            // Show modal
            overlay.style.display = 'flex';
            overlay.style.opacity = '0';

            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }

            requestAnimationFrame(() => {
                setTimeout(() => {
                    overlay.style.opacity = '1';
                    const modalContent = overlay.querySelector('.math-modal-content');
                    if (modalContent) {
                        modalContent.style.opacity = '1';
                        modalContent.style.transform = 'scale(1)';
                    }
                }, 10);
            });

        } catch (fallbackError) {
            console.error('Error showing fallback modal:', fallbackError);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
        }
    }

    /**
     * Generate default placement recommendation
     */
    function getDefaultPlacementRecommendation(score, highestLevel) {
        if (score >= 80 || highestLevel === 'GCSEPart2') {
            return {
                recommendedLevel: 'Champions',
                specificCourses: ['ICDL Level 3', 'Advanced Mathematical Applications', 'Computer Skills for Work Level 3'],
                reasoning: 'Exceptional performance across all assessment levels demonstrates readiness for advanced mathematical studies.',
                nextSteps: ['Enroll in Champions level courses', 'Consider advanced mathematics pathway', 'Explore specialized mathematical applications']
            };
        } else if (score >= 60 || highestLevel === 'GCSEPart1' || highestLevel === 'Level1') {
            return {
                recommendedLevel: 'Intermediate',
                specificCourses: ['ICDL Level 2', 'Computer Skills for Work Level 2', 'Improvers Plus'],
                reasoning: 'Strong mathematics foundation qualifies you for intermediate-level courses with practical applications.',
                nextSteps: ['Continue building on your solid mathematical foundation', 'Enroll in intermediate courses', 'Practice advanced problem-solving']
            };
        } else if (score >= 40 || highestLevel === 'Entry') {
            return {
                recommendedLevel: 'Improvers',
                specificCourses: ['Computer Skills Beginners Plus', 'Everyday Life Level 1', 'Mathematics Skills Development'],
                reasoning: 'Good basic mathematics skills qualify you for foundational courses and skill development.',
                nextSteps: ['Focus on strengthening mathematical foundation', 'Complete foundational courses', 'Practice regularly before advancing']
            };
        } else {
            return {
                recommendedLevel: 'Essentials',
                specificCourses: ['Computer Skills Beginners', 'Basic Numeracy Support', 'Foundation Mathematics'],
                reasoning: 'Entry-level assessment indicates need for foundational mathematical skills development before progressing to higher levels.',
                nextSteps: ['Complete Essentials level courses', 'Practice basic arithmetic daily', 'Retake assessment after skill development']
            };
        }
    }

    /**
     * Generate default feedback based on score
     */
    function getDefaultMathFeedback(score) {
        if (score >= 80) {
            return {
                numericalSkills: 'Excellent numerical computation and problem-solving abilities demonstrated.',
                algebraicThinking: 'Strong algebraic reasoning with complex equation manipulation skills.',
                problemSolving: 'Outstanding analytical approach to mathematical word problems.',
                geometricReasoning: 'Advanced spatial reasoning and geometric understanding.',
                dataHandling: 'Proficient in statistical analysis and data interpretation.',
                overall: 'Exceptional mathematics proficiency at GCSE Higher level. Ready for advanced mathematical applications.'
            };
        } else if (score >= 60) {
            return {
                numericalSkills: 'Good numerical skills with accurate basic calculations.',
                algebraicThinking: 'Solid understanding of algebraic concepts and methods.',
                problemSolving: 'Effective approach to mathematical problem-solving.',
                geometricReasoning: 'Good spatial awareness and geometric understanding.',
                dataHandling: 'Adequate data handling and basic statistical skills.',
                overall: 'Strong mathematics proficiency at GCSE Foundation level. Well-prepared for practical applications.'
            };
        } else if (score >= 40) {
            return {
                numericalSkills: 'Adequate numerical skills for everyday calculations.',
                algebraicThinking: 'Basic algebraic understanding with room for development.',
                problemSolving: 'Developing problem-solving strategies and mathematical reasoning.',
                geometricReasoning: 'Foundational geometric concepts understood.',
                dataHandling: 'Basic data interpretation skills demonstrated.',
                overall: 'Solid mathematics foundation at Level 1. Some areas for improvement before advanced training.'
            };
        } else {
            return {
                numericalSkills: 'Basic numerical understanding with opportunities for improvement.',
                algebraicThinking: 'Foundational algebraic concepts that would benefit from development.',
                problemSolving: 'Simple problem-solving approach with potential for enhancement.',
                geometricReasoning: 'Basic geometric awareness with room for growth.',
                dataHandling: 'Elementary data handling skills requiring further development.',
                overall: 'Entry level mathematics proficiency. Focused mathematical development recommended.'
            };
        }
    }

    /**
     * Generate default strengths based on score
     */
    function getDefaultMathStrengths(score) {
        if (score >= 80) {
            return [
                'Excellent mathematical reasoning skills',
                'Strong problem-solving abilities',
                'Advanced algebraic manipulation',
                'Proficient geometric understanding',
                'Effective data analysis skills',
                'Ready for complex mathematical applications'
            ];
        } else if (score >= 60) {
            return [
                'Good mathematical foundation',
                'Solid numerical computation skills',
                'Effective basic problem-solving',
                'Clear understanding of key concepts'
            ];
        } else if (score >= 40) {
            return [
                'Adequate basic mathematical skills',
                'Understanding of fundamental concepts',
                'Willingness to engage with mathematical problems'
            ];
        } else {
            return [
                'Completed the mathematics assessment',
                'Basic mathematical concepts attempted',
                'Foundation for improvement established'
            ];
        }
    }

    /**
     * Generate default improvements based on score
     */
    function getDefaultMathImprovements(score) {
        if (score >= 80) {
            return [
                'Continue practicing advanced mathematical techniques',
                'Explore specialized mathematical applications',
                'Maintain current proficiency level'
            ];
        } else if (score >= 60) {
            return [
                'Practice complex problem-solving strategies',
                'Strengthen algebraic manipulation skills',
                'Work on advanced geometric concepts',
                'Consider additional mathematical support'
            ];
        } else if (score >= 40) {
            return [
                'Focus on strengthening numerical skills',
                'Practice algebraic problem-solving',
                'Work on geometric reasoning',
                'Consider mathematics support courses'
            ];
        } else {
            return [
                'Focus on basic numerical operations',
                'Build foundational mathematical vocabulary',
                'Practice simple problem-solving',
                'Consider mathematics foundation courses',
                'Regular mathematical practice recommended'
            ];
        }
    }

    /**
     * Generate default course recommendations based on score and level
     */
    function getDefaultMathCourseRecommendations(score, level) {
        if (score >= 80 || level === 'GCSE Higher') {
            return {
                description: 'Excellent mathematics proficiency qualifies you for advanced mathematical applications and specialized courses.',
                eligible: [
                    'Advanced Mathematics Applications',
                    'Statistics and Data Analysis',
                    'Engineering Mathematics',
                    'Financial Mathematics',
                    'Computer Science Mathematics'
                ],
                nextSteps: 'You are ready for advanced mathematical training. Consider specialized applications based on your career interests.'
            };
        } else if (score >= 60 || level === 'GCSE Foundation') {
            return {
                description: 'Strong mathematics foundation qualifies you for practical mathematical applications and further development.',
                eligible: [
                    'Practical Mathematics Applications',
                    'Business Mathematics',
                    'Technical Mathematics',
                    'Mathematics for Trades',
                    'Foundation Statistics'
                ],
                nextSteps: 'Continue building on your solid mathematical foundation with practical applications.'
            };
        } else if (score >= 40 || level === 'Level 1') {
            return {
                description: 'Good basic mathematics skills qualify you for foundational courses and skill development.',
                eligible: [
                    'Mathematics Skills Development',
                    'Functional Mathematics',
                    'Workplace Mathematics',
                    'Basic Statistics',
                    'Mathematics Foundation Course'
                ],
                nextSteps: 'Focus on strengthening your mathematical foundation before advancing to specialized applications.'
            };
        } else {
            return {
                description: 'Entry level mathematics requires foundational development before progressing to advanced courses.',
                eligible: [
                    'Basic Mathematics Skills',
                    'Numeracy Development',
                    'Mathematics Foundation',
                    'Essential Mathematics',
                    'Mathematics Support Course'
                ],
                nextSteps: 'Begin with foundational mathematics courses to build essential skills for future learning.'
            };
        }
    }

    /**
     * Create and show the modal
     */
    async function createModal(userName) {
        // Remove existing modal if any
        const existingOverlay = document.getElementById('math-results-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create modal HTML
        const modalHTML = createModalHTML(userName);

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add styles if not already added
        addModalStyles();

        // Initialize event listeners
        const overlay = document.getElementById('math-results-overlay');
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in with skills gap modal style
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.math-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Reset and show existing modal
     */
    async function resetAndShowModal(userName) {
        const overlay = document.getElementById('math-results-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new data
        overlay.innerHTML = createModalContent(userName);

        // Re-initialize event listeners
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in with skills gap modal style
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.math-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Create modal HTML structure
     */
    function createModalHTML(userName) {
        return `
            <div id="math-results-overlay" class="math-modal-overlay" data-user-email="${currentUserEmail || ''}">
                ${createModalContent(userName)}
            </div>
        `;
    }

    /**
     * Create modal content
     */
    function createModalContent(userName) {
        if (!currentMathData) {
            return createErrorContent();
        }

        // Determine display values based on overrides
        const originalScore = currentMathData.mathOverallScore || 0;
        const originalLevel = currentMathData.mathCurrentLevel || 'Entry';
        const hasScoreOverride = currentMathData.manualScoreOverride;
        const hasLevelOverride = currentMathData.manualLevelOverride;

        // Use overridden values if available
        const displayScore = hasScoreOverride ? currentMathData.manualScoreOverride.newScore : originalScore;
        const displayLevel = hasScoreOverride ?
            (typeof window.calculateMathLevelFromScore === 'function' ?
                window.calculateMathLevelFromScore(currentMathData.manualScoreOverride.newScore) :
                currentMathData.manualScoreOverride.calculatedLevel) :
            (hasLevelOverride ? currentMathData.manualLevelOverride.newLevel : originalLevel);

        const isQualified = displayScore >= 60; // Adjust threshold as needed
        const score = displayScore;
        const level = displayLevel;
        const feedback = currentMathData.mathFeedback || {};
        const strengths = currentMathData.mathStrengths || [];
        const improvements = currentMathData.mathImprovements || [];

        // Use updated course recommendations if score override is applied
        let courseRecommendations;
        if (hasScoreOverride) {
            courseRecommendations = getDefaultMathCourseRecommendations(displayScore, displayLevel);
        } else if (hasLevelOverride) {
            courseRecommendations = getDefaultMathCourseRecommendations(originalScore, displayLevel);
        } else {
            courseRecommendations = currentMathData.courseRecommendations || getDefaultMathCourseRecommendations(originalScore, originalLevel);
        }

        const timeSpent = currentMathData.totalTimeSpentOnMath || 0;
        const timestamp = currentMathData.mathAssessmentTimestamp;
        const userName = currentMathData.firstName && currentMathData.lastName ?
            `${currentMathData.firstName} ${currentMathData.lastName}` : 'Student';

        // Format timestamp
        const formattedDate = timestamp ? new Date(timestamp.toDate ? timestamp.toDate() : timestamp).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Date not available';

        // Format time spent
        const formattedTime = timeSpent > 0 ? `${Math.round(timeSpent / 60)} minutes` : 'Time not recorded';

        return `
            <div class="math-modal-content">
                <div class="math-modal-header">
                    <div class="math-modal-title-container">
                        <h2 class="math-modal-employee-title">${userName}</h2>
                        <h3 class="math-modal-subtitle">Mathematics Assessment Results</h3>
                    </div>
                    <div class="math-modal-actions">
                        <button id="close-math-modal" class="math-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="math-modal-body">
                    ${createScoreOverviewSection(score, level, isQualified, hasScoreOverride, hasLevelOverride, formattedDate, formattedTime)}
                    ${createDebugDataSection()}
                    ${createLevelProgressionSection()}
                    ${createTopicPerformanceSection()}
                    ${createPerformanceAnalysisSection(feedback)}
                    ${createCourseRecommendationsSection(courseRecommendations)}
                    ${createStrengthsImprovementsSection(strengths, improvements)}
                    ${createTimeAnalyticsSection(timeSpent, timestamp)}
                    ${createNextStepsSection(isQualified, score)}
                    ${createAdminReviewSection()}
                </div>
            </div>
        `;
    }

    /**
     * Create error content when data is not available
     */
    function createErrorContent() {
        return `
            <div class="math-modal-content">
                <div class="math-modal-header">
                    <div class="math-modal-title-container">
                        <h2 class="math-modal-employee-title">Mathematics Assessment Results</h2>
                        <h3 class="math-modal-subtitle">Data Not Available</h3>
                    </div>
                    <div class="math-modal-actions">
                        <button id="close-math-modal" class="math-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="math-modal-body">
                    <div class="math-error-content">
                        <div class="math-error-icon">
                            <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                        </div>
                        <h3>Assessment Data Unavailable</h3>
                        <p>Unable to load mathematics assessment results. Please try again later or contact support if the problem persists.</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create score overview section
     */
    function createScoreOverviewSection(score, level, isQualified, hasScoreOverride, hasLevelOverride, formattedDate, formattedTime) {
        return `
            <!-- Score Overview Section -->
            <div class="math-score-overview ${isQualified ? 'qualified' : 'needs-improvement'}">
                <div class="math-score-display">
                    <div class="math-score-value ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${score}/100</div>
                    <div class="math-score-level ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${level} Level</div>
                    ${hasScoreOverride ? '<div class="override-indicator">Score Override Applied</div>' : ''}
                    ${hasLevelOverride && !hasScoreOverride ? '<div class="override-indicator">Level Override Applied</div>' : ''}
                    <div class="math-score-status ${isQualified ? 'qualified' : 'needs-improvement'}">
                        ${isQualified ? 'Qualified for Advanced Mathematics Training' : 'Additional Mathematics Support Recommended'}
                    </div>
                </div>
                <div class="math-assessment-meta">
                    <div class="math-meta-item">
                        <span class="math-meta-label">Completed:</span>
                        <span class="math-meta-value">${formattedDate}</span>
                    </div>
                    <div class="math-meta-item">
                        <span class="math-meta-label">Time Spent:</span>
                        <span class="math-meta-value">${formattedTime}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create results summary section
     */
    function createResultsSummary() {
        const displayLevel = currentMathData.placementRecommendation?.level || currentMathData.currentLevel;
        const isQualified = currentMathData.overallScore >= 60; // Adjust threshold as needed

        return `
            <div class="math-results-summary">
                <div class="math-results-score-display">
                    <div class="math-results-score-value">${currentMathData.overallScore} points</div>
                    <div class="math-results-level-display">
                        <span class="math-results-level">${displayLevel}</span>
                    </div>
                </div>
                <div class="math-results-status">
                    <span class="math-results-status-badge ${isQualified ? 'qualified' : 'needs-improvement'}">
                        ${isQualified ? 'Qualified for Advanced Mathematics' : 'Additional Mathematics Support Recommended'}
                    </span>
                </div>
                <div class="math-results-metadata">
                    <div class="math-results-metadata-item">
                        <span class="math-results-metadata-label">Highest Level:</span>
                        <span class="math-results-metadata-value">${currentMathData.highestLevelCompleted || 'Entry'}</span>
                    </div>
                    <div class="math-results-metadata-item">
                        <span class="math-results-metadata-label">Time Spent:</span>
                        <span class="math-results-metadata-value">${formatDuration(currentMathData.totalTimeSpent)}</span>
                    </div>
                    <div class="math-results-metadata-item">
                        <span class="math-results-metadata-label">Completed:</span>
                        <span class="math-results-metadata-value">${formatDate(currentMathData.timestamp)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create debug data section (temporary for troubleshooting)
     */
    function createDebugDataSection() {
        if (!currentMathData) return '';

        // Only show in development environment
        if (window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1')) {
            return '';
        }

        return `
            <!-- Debug Data Section (Development Only) -->
            <div class="math-debug-section" style="margin-bottom: 1.5rem; border: 2px dashed #f59e0b; padding: 1rem; background: #fffbeb;">
                <h3 style="font-size: 0.9rem; font-weight: 600; color: #b45309; margin-bottom: 0.75rem;">Debug Data Structure</h3>
                <details>
                    <summary style="cursor: pointer; font-weight: 500; color: #b45309;">Click to view raw data structure</summary>
                    <pre style="background: #fff; padding: 1rem; border-radius: 4px; overflow: auto; max-height: 300px; font-size: 0.7rem; margin-top: 0.5rem;">${JSON.stringify(currentMathData, null, 2)}</pre>
                </details>
                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #b45309;">
                    <p>This section is only visible in development environments.</p>
                </div>
            </div>
        `;
    }

    /**
     * Create level progression visualization section
     */
    function createLevelProgressionSection() {
        if (!currentMathData) return '';

        const levels = [
            { key: 'mathEntryLevel', name: 'Entry Level', maxScore: 32, description: 'Basic arithmetic and foundational skills' },
            { key: 'mathLevel1', name: 'Level 1', maxScore: 25, description: 'Intermediate mathematical concepts' },
            { key: 'mathGCSEPart1', name: 'GCSE Part 1', maxScore: 30, description: 'Foundation GCSE mathematics' },
            { key: 'mathGCSEPart2', name: 'GCSE Part 2', maxScore: 20, description: 'Higher GCSE mathematics' }
        ];

        const progressHTML = levels.map(levelInfo => {
            const levelData = currentMathData[levelInfo.key];
            const completed = levelData?.completed || false;
            const score = levelData?.score || 0;
            const passed = levelData?.passed || false;
            const percentage = completed ? Math.round((score / levelInfo.maxScore) * 100) : 0;

            return `
                <div class="math-level-progress-item ${completed ? (passed ? 'completed-passed' : 'completed-failed') : 'not-attempted'}">
                    <div class="math-level-header">
                        <div class="math-level-info">
                            <h4>${levelInfo.name}</h4>
                            <p class="math-level-description">${levelInfo.description}</p>
                        </div>
                        <div class="math-level-status">
                            ${completed ?
                                `<span class="status-badge ${passed ? 'passed' : 'failed'}">${passed ? 'PASSED' : 'FAILED'}</span>` :
                                '<span class="status-badge not-attempted">NOT ATTEMPTED</span>'
                            }
                        </div>
                    </div>
                    <div class="math-level-progress">
                        <div class="progress-bar">
                            <div class="progress-fill ${passed ? 'passed' : 'failed'}" style="width: ${percentage}%"></div>
                        </div>
                        <div class="progress-text">
                            ${completed ? `${score}/${levelInfo.maxScore} (${percentage}%)` : `0/${levelInfo.maxScore} (0%)`}
                        </div>
                    </div>
                    ${completed && levelData.timeSpent ? `
                        <div class="math-level-time">
                            <span class="time-label">Time spent:</span>
                            <span class="time-value">${Math.round(levelData.timeSpent / 60000)} minutes</span>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        return `
            <!-- Level Progression Section -->
            <div class="math-level-progression">
                <h3>Assessment Level Progression</h3>
                <div class="math-progression-grid">
                    ${progressHTML}
                </div>
                <div class="math-progression-summary">
                    <p>Highest Level Completed: <strong>${currentMathData.mathHighestLevelCompleted || 'None'}</strong></p>
                    <p>Current Placement Level: <strong>${currentMathData.mathCurrentLevel || 'Entry'}</strong></p>
                </div>
            </div>
        `;
    }

    /**
     * Create topic performance breakdown section
     */
    function createTopicPerformanceSection() {
        if (!currentMathData) return '';

        const topicData = [];

        // Collect topic data from all completed levels
        const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];

        levels.forEach(levelKey => {
            const levelData = currentMathData[levelKey];
            if (levelData?.completed && levelData.topicBreakdown && typeof levelData.topicBreakdown === 'object') {
                // Log the topic breakdown structure for debugging
                console.log(`Topic breakdown for ${levelKey}:`, levelData.topicBreakdown);

                Object.entries(levelData.topicBreakdown).forEach(([topic, data]) => {
                    // Handle different possible data structures
                    let score = 0;
                    let maxScore = 0;

                    // Case 1: { score: X, maxScore: Y }
                    if (data && typeof data === 'object' && 'score' in data && 'maxScore' in data) {
                        score = data.score || 0;
                        maxScore = data.maxScore || 0;
                    }
                    // Case 2: { correct: X, total: Y }
                    else if (data && typeof data === 'object' && 'correct' in data && 'total' in data) {
                        score = data.correct || 0;
                        maxScore = data.total || 0;
                    }
                    // Case 3: Just a number (score)
                    else if (typeof data === 'number') {
                        score = data;
                        // Use default max scores based on level and topic
                        const defaultMaxScores = {
                            'Entry': { arithmetic: 8, fractions: 6, percentages: 4, basicAlgebra: 6, measurement: 4, dataHandling: 4 },
                            'Level1': { advancedArithmetic: 4, fractionsDecimals: 4, percentagesRatio: 4, algebraicExpressions: 6, geometry: 4, statistics: 4 },
                            'GCSEPart1': { numberOperations: 3, algebraicManipulation: 3, geometricReasoning: 2, fractionalCalculations: 2 },
                            'GCSEPart2': { complexCalculations: 4, statisticalAnalysis: 4, trigonometry: 4, advancedAlgebra: 4, problemSolving: 4 }
                        };

                        const levelKey = levelKey.replace('math', '');
                        maxScore = defaultMaxScores[levelKey] && defaultMaxScores[levelKey][topic] ?
                            defaultMaxScores[levelKey][topic] : 5; // Default to 5 if unknown
                    }

                    const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;

                    topicData.push({
                        level: levelKey.replace('math', ''),
                        topic: formatTopicName(topic),
                        score: score,
                        maxScore: maxScore,
                        percentage: percentage
                    });
                });
            }
        });

        if (topicData.length === 0) {
            return `
                <div class="math-topic-performance">
                    <h3>Topic Performance Breakdown</h3>
                    <p class="no-topic-data">No topic performance data available. Complete assessment levels to see detailed topic analysis.</p>
                </div>
            `;
        }

        const topicHTML = topicData.map(topic => {
            // Ensure all values are properly formatted
            const score = typeof topic.score === 'number' ? topic.score : 0;
            const maxScore = typeof topic.maxScore === 'number' ? topic.maxScore : 0;
            const percentage = typeof topic.percentage === 'number' ? topic.percentage : 0;
            const status = percentage >= 70 ? 'excellent' : percentage >= 50 ? 'good' : 'needs-work';

            return `
            <div class="math-topic-item">
                <div class="topic-header">
                    <span class="topic-name">${topic.topic}</span>
                    <span class="topic-level">${topic.level}</span>
                </div>
                <div class="topic-progress">
                    <div class="topic-progress-bar">
                        <div class="topic-progress-fill ${status}"
                             style="width: ${percentage}%"></div>
                    </div>
                    <div class="topic-score">${score}/${maxScore} (${percentage}%)</div>
                </div>
            </div>
            `;
        }).join('');

        return `
            <!-- Topic Performance Section -->
            <div class="math-topic-performance">
                <h3>Topic Performance Breakdown</h3>
                <div class="math-topics-grid">
                    ${topicHTML}
                </div>
                <div class="topic-legend">
                    <div class="legend-item">
                        <span class="legend-color excellent"></span>
                        <span>Excellent (70%+)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color good"></span>
                        <span>Good (50-69%)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color needs-work"></span>
                        <span>Needs Work (<50%)</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create time analytics section
     */
    function createTimeAnalyticsSection(totalTime, timestamp) {
        const formattedTotalTime = totalTime > 0 ? `${Math.round(totalTime / 60)} minutes` : 'Time not recorded';
        const formattedDate = timestamp ? new Date(timestamp.toDate ? timestamp.toDate() : timestamp).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Date not available';

        // Calculate time spent per level
        const levelTimes = [];
        const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];

        levels.forEach(levelKey => {
            const levelData = currentMathData[levelKey];
            if (levelData?.completed && levelData.timeSpent) {
                levelTimes.push({
                    level: levelKey.replace('math', ''),
                    time: Math.round(levelData.timeSpent / 60000),
                    completed: levelData.completedAt ? new Date(levelData.completedAt.toDate ? levelData.completedAt.toDate() : levelData.completedAt).toLocaleDateString('en-GB') : 'Unknown'
                });
            }
        });

        const levelTimeHTML = levelTimes.length > 0 ? levelTimes.map(level => `
            <div class="time-breakdown-item">
                <span class="time-level">${level.level}:</span>
                <span class="time-duration">${level.time} minutes</span>
                <span class="time-date">(${level.completed})</span>
            </div>
        `).join('') : '<p class="no-time-data">No detailed time breakdown available.</p>';

        return `
            <!-- Time Analytics Section -->
            <div class="math-time-analytics">
                <h3>Time Analytics</h3>
                <div class="time-overview">
                    <div class="time-summary-item">
                        <span class="time-label">Total Assessment Time:</span>
                        <span class="time-value">${formattedTotalTime}</span>
                    </div>
                    <div class="time-summary-item">
                        <span class="time-label">Assessment Completed:</span>
                        <span class="time-value">${formattedDate}</span>
                    </div>
                </div>
                <div class="time-breakdown">
                    <h4>Time Spent by Level</h4>
                    ${levelTimeHTML}
                </div>
            </div>
        `;
    }

    /**
     * Create performance analysis section
     */
    function createPerformanceAnalysisSection(feedback) {
        return `
            <!-- Performance Analysis Section -->
            <div class="math-analysis-section">
                <h3>Performance Analysis</h3>
                <div class="math-feedback-grid">
                    <div class="math-feedback-item">
                        <h4>Numerical Skills</h4>
                        <p>${feedback.numericalSkills || 'Assessment completed'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Algebraic Thinking</h4>
                        <p>${feedback.algebraicThinking || 'Algebraic reasoning evaluated'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Problem Solving</h4>
                        <p>${feedback.problemSolving || 'Problem-solving assessed'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Geometric Reasoning</h4>
                        <p>${feedback.geometricReasoning || 'Geometric understanding evaluated'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Data Handling</h4>
                        <p>${feedback.dataHandling || 'Data interpretation assessed'}</p>
                    </div>
                </div>
                <div class="math-overall-feedback">
                    <h4>Overall Assessment</h4>
                    <p>${feedback.overall || 'Mathematics assessment completed successfully'}</p>
                </div>
            </div>
        `;
    }

    /**
     * Create course recommendations section
     */
    function createCourseRecommendationsSection(courseRecommendations) {
        return `
            <!-- Course Recommendations Section -->
            <div class="math-course-recommendations">
                <h3>Course Recommendations</h3>
                <div class="math-recommendations-content">
                    <div class="math-recommendations-description">
                        <p class="course-description">${courseRecommendations.description || 'Course recommendations based on assessment results.'}</p>
                    </div>
                    <div class="math-eligible-courses">
                        <h4>Eligible Courses</h4>
                        <div class="math-courses-grid eligible-courses">
                            ${(courseRecommendations.eligible || []).map(course => `
                                <div class="math-course-item">
                                    <span class="math-course-icon">📊</span>
                                    <span class="math-course-name">${course}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="math-next-steps-recommendation">
                        <h4>Next Steps</h4>
                        <p class="course-next-steps">${courseRecommendations.nextSteps || 'Contact your administrator for course enrollment information.'}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create strengths and improvements section
     */
    function createStrengthsImprovementsSection(strengths, improvements) {
        return `
            <!-- Strengths and Improvements Section -->
            <div class="math-strengths-improvements">
                <div class="math-strengths-section">
                    <h4>Identified Strengths</h4>
                    <ul class="math-strengths-list">
                        ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                    </ul>
                </div>
                <div class="math-improvements-section">
                    <h4>Areas for Development</h4>
                    <ul class="math-improvements-list">
                        ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * Create next steps section
     */
    function createNextStepsSection(isQualified, score) {
        return `
            <!-- Next Steps Section -->
            <div class="math-next-steps">
                <h4>Recommended Next Steps</h4>
                <p class="math-next-steps-text">
                    ${getNextStepsMessage(isQualified, score)}
                </p>
            </div>
        `;
    }

    /**
     * Create admin review section
     */
    function createAdminReviewSection() {
        return `
            <!-- Admin Review Section -->
            <div class="math-admin-review">
                <h4>Admin Review Tools</h4>
                <div class="admin-review-buttons">
                    <button id="view-detailed-math-responses" class="admin-review-btn primary">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        View Detailed Responses
                    </button>
                    <button id="show-basic-math-review" class="admin-review-btn secondary" style="display: none;">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Show Basic Review
                    </button>
                    ${currentMathData.hasDetailedResponses ? `
                        <span class="responses-available">✓ Detailed responses available</span>
                    ` : `
                        <span class="responses-unavailable">⚠ Limited response data</span>
                    `}
                </div>
                <div class="admin-review-info">
                    <p>Use these tools to manually review student responses and override assessment levels if needed.</p>
                </div>
            </div>
        `;
    }

    /**
     * Format topic names for display
     */
    function formatTopicName(topic) {
        const topicNames = {
            'arithmetic': 'Arithmetic',
            'fractions': 'Fractions',
            'percentages': 'Percentages',
            'basicAlgebra': 'Basic Algebra',
            'measurement': 'Measurement',
            'dataHandling': 'Data Handling',
            'advancedArithmetic': 'Advanced Arithmetic',
            'fractionsDecimals': 'Fractions & Decimals',
            'percentagesRatio': 'Percentages & Ratio',
            'algebraicExpressions': 'Algebraic Expressions',
            'geometry': 'Geometry',
            'statistics': 'Statistics',
            'numberOperations': 'Number Operations',
            'algebraicManipulation': 'Algebraic Manipulation',
            'geometricReasoning': 'Geometric Reasoning',
            'fractionalCalculations': 'Fractional Calculations',
            'complexCalculations': 'Complex Calculations',
            'statisticalAnalysis': 'Statistical Analysis',
            'trigonometry': 'Trigonometry',
            'advancedAlgebra': 'Advanced Algebra',
            'problemSolving': 'Problem Solving'
        };

        return topicNames[topic] || topic.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // Validation helper functions
    function validateLevel(level) {
        const validLevels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2', 'GCSE Foundation', 'GCSE Higher'];
        return validLevels.includes(level) ? level : null;
    }

    function validateScore(score) {
        const numScore = Number(score);
        return !isNaN(numScore) && numScore >= 0 && numScore <= 100 ? numScore : null;
    }

    function validateTimestamp(timestamp) {
        if (!timestamp) return null;
        if (timestamp.toDate && typeof timestamp.toDate === 'function') return timestamp;
        if (timestamp instanceof Date) return timestamp;
        const date = new Date(timestamp);
        return !isNaN(date.getTime()) ? date : null;
    }

    function validateTimeSpent(timeSpent) {
        const numTime = Number(timeSpent);
        return !isNaN(numTime) && numTime >= 0 ? numTime : null;
    }

    function validateString(str) {
        return typeof str === 'string' && str.trim().length > 0 ? str.trim() : null;
    }

    function validateArray(arr) {
        return Array.isArray(arr) ? arr : null;
    }

    function validateFeedback(feedback) {
        if (!feedback || typeof feedback !== 'object') return null;
        return feedback;
    }

    function validatePlacementRecommendation(recommendation) {
        if (!recommendation || typeof recommendation !== 'object') return null;
        return recommendation;
    }

    function validateCourseRecommendations(recommendations) {
        if (!recommendations || typeof recommendations !== 'object') return null;
        return recommendations;
    }

    function validateOverride(override) {
        if (!override || typeof override !== 'object') return null;
        return override;
    }

    function validateLevelData(levelData, levelType) {
        if (!levelData || typeof levelData !== 'object') {
            return createDefaultLevelData(levelType);
        }

        return {
            completed: Boolean(levelData.completed),
            score: validateScore(levelData.score) || 0,
            passed: Boolean(levelData.passed),
            timeSpent: validateTimeSpent(levelData.timeSpent) || 0,
            completedAt: validateTimestamp(levelData.completedAt) || null,
            responses: validateArray(levelData.responses) || [],
            topicBreakdown: validateTopicBreakdown(levelData.topicBreakdown, levelType)
        };
    }

    function validateTopicBreakdown(breakdown, levelType) {
        const defaultBreakdowns = {
            'Entry': {
                arithmetic: { score: 0, maxScore: 8 },
                fractions: { score: 0, maxScore: 6 },
                percentages: { score: 0, maxScore: 4 },
                basicAlgebra: { score: 0, maxScore: 6 },
                measurement: { score: 0, maxScore: 4 },
                dataHandling: { score: 0, maxScore: 4 }
            },
            'Level1': {
                advancedArithmetic: { score: 0, maxScore: 4 },
                fractionsDecimals: { score: 0, maxScore: 4 },
                percentagesRatio: { score: 0, maxScore: 4 },
                algebraicExpressions: { score: 0, maxScore: 6 },
                geometry: { score: 0, maxScore: 4 },
                statistics: { score: 0, maxScore: 4 }
            },
            'GCSEPart1': {
                numberOperations: { score: 0, maxScore: 3 },
                algebraicManipulation: { score: 0, maxScore: 3 },
                geometricReasoning: { score: 0, maxScore: 2 },
                fractionalCalculations: { score: 0, maxScore: 2 }
            },
            'GCSEPart2': {
                complexCalculations: { score: 0, maxScore: 4 },
                statisticalAnalysis: { score: 0, maxScore: 4 },
                trigonometry: { score: 0, maxScore: 4 },
                advancedAlgebra: { score: 0, maxScore: 4 },
                problemSolving: { score: 0, maxScore: 4 }
            }
        };

        const defaultBreakdown = defaultBreakdowns[levelType] || {};

        if (!breakdown || typeof breakdown !== 'object') {
            return defaultBreakdown;
        }

        // Validate each topic in the breakdown
        const validatedBreakdown = {};
        Object.keys(defaultBreakdown).forEach(topic => {
            if (breakdown[topic] && typeof breakdown[topic] === 'object') {
                validatedBreakdown[topic] = {
                    score: validateScore(breakdown[topic].score) || 0,
                    maxScore: validateScore(breakdown[topic].maxScore) || defaultBreakdown[topic].maxScore
                };
            } else {
                validatedBreakdown[topic] = defaultBreakdown[topic];
            }
        });

        return validatedBreakdown;
    }

    function createDefaultLevelData(levelType) {
        return {
            completed: false,
            score: 0,
            passed: false,
            timeSpent: 0,
            completedAt: null,
            responses: [],
            topicBreakdown: validateTopicBreakdown(null, levelType)
        };
    }

    /**
     * Get next steps message based on qualification status
     */
    function getNextStepsMessage(isQualified, score) {
        if (score >= 80) {
            return 'Excellent mathematics proficiency! You are ready for advanced mathematical applications and specialized training programs. Consider exploring areas like statistics, engineering mathematics, or financial mathematics based on your interests.';
        } else if (score >= 60) {
            return 'Strong mathematics foundation! You qualify for practical mathematical applications and can progress to intermediate-level courses. Continue building on your solid mathematical skills.';
        } else if (score >= 40) {
            return 'Good basic mathematics skills! Focus on strengthening your foundation with additional practice and support. Consider enrolling in mathematics skills development courses before advancing to specialized applications.';
        } else {
            return 'Mathematics foundation development recommended. Begin with basic mathematics courses to build essential skills. With focused study and practice, you can develop the mathematical proficiency needed for advanced training.';
        }
    }

    /**
     * Create level results section (legacy support)
     */
    function createLevelResults() {
        const levels = ['entryLevel', 'level1', 'gcsePart1', 'gcsePart2'];
        const levelNames = {
            'entryLevel': 'Entry Level',
            'level1': 'Level 1',
            'gcsePart1': 'GCSE Part 1',
            'gcsePart2': 'GCSE Part 2'
        };

        const completedLevels = levels.filter(level => currentMathData[level] && currentMathData[level].completed);

        if (completedLevels.length === 0) {
            return '<div class="math-results-levels"><p>No level assessments completed.</p></div>';
        }

        return `
            <div class="math-results-levels">
                <h3>Assessment Results by Level</h3>
                <div class="math-results-levels-grid">
                    ${completedLevels.map(level => {
                        const levelData = currentMathData[level];
                        const passed = levelData.passed ? 'passed' : 'failed';

                        return `
                            <div class="math-results-level-card ${passed}">
                                <div class="math-results-level-header">
                                    <h4>${levelNames[level]}</h4>
                                    <span class="math-results-level-status ${passed}">${passed.toUpperCase()}</span>
                                </div>
                                <div class="math-results-level-details">
                                    <div class="math-results-level-score">Score: ${levelData.score}/${getMaxScore(level)}</div>
                                    <div class="math-results-level-time">Time: ${formatDuration(levelData.timeSpent)}</div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Create recommendations section
     */
    function createRecommendations() {
        const strengths = currentMathData.strengths || [];
        const improvements = currentMathData.improvements || [];
        const placement = currentMathData.placementRecommendation;

        return `
            <div class="math-results-recommendations">
                <h3>Assessment Analysis</h3>

                <div class="math-results-analysis-grid">
                    ${strengths.length > 0 ? `
                        <div class="math-results-strengths">
                            <h4>Strengths</h4>
                            <ul>
                                ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${improvements.length > 0 ? `
                        <div class="math-results-improvements">
                            <h4>Areas for Development</h4>
                            <ul>
                                ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>

                ${placement ? `
                    <div class="math-results-placement">
                        <h4>Recommended Placement</h4>
                        <div class="math-results-placement-level">${placement.level}</div>
                        <p>${placement.reasoning || ''}</p>
                        ${placement.nextSteps && placement.nextSteps.length > 0 ? `
                            <div class="math-results-next-steps">
                                <h5>Next Steps:</h5>
                                <ul>
                                    ${placement.nextSteps.map(step => `<li>${step}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Helper functions
     */
    function formatDuration(seconds) {
        if (!seconds) return '0 minutes';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        if (minutes === 0) return `${remainingSeconds} seconds`;
        if (remainingSeconds === 0) return `${minutes} minutes`;
        return `${minutes}m ${remainingSeconds}s`;
    }

    function formatDate(timestamp) {
        if (!timestamp) return 'Not available';
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    function getMaxScore(level) {
        const maxScores = {
            'entryLevel': 44,
            'level1': 26,
            'gcsePart1': 10,
            'gcsePart2': 20
        };
        return maxScores[level] || 0;
    }

    /**
     * Initialize event listeners
     */
    function initializeEventListeners(overlay) {
        // Close modal on overlay click
        overlay.addEventListener('click', overlayClickHandler);

        // Close button
        const closeBtn = overlay.querySelector('#close-math-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', hideModal);
        }

        // Admin review buttons
        const viewResponsesBtn = overlay.querySelector('#view-detailed-math-responses');
        if (viewResponsesBtn) {
            viewResponsesBtn.addEventListener('click', handleViewDetailedResponses);
        }

        const showBasicBtn = overlay.querySelector('#show-basic-math-review');
        if (showBasicBtn) {
            showBasicBtn.addEventListener('click', handleShowBasicReview);
        }
    }

    /**
     * Handle overlay click to close modal
     */
    function overlayClickHandler(event) {
        if (event.target.id === 'math-results-overlay') {
            hideModal();
        }
    }

    /**
     * Handle view detailed responses
     */
    function handleViewDetailedResponses() {
        if (!currentMathData || !currentMathData.responses) {
            if (typeof showNotification === 'function') {
                showNotification('No detailed response data available', 'error');
            }
            return;
        }

        // Show detailed responses in a new window (similar to English modal)
        const responses = currentMathData.responses.questionResponses || [];
        let reviewText = `Mathematics Assessment Review - ${currentMathData.userName}\n`;
        reviewText += `Email: ${currentMathData.userEmail}\n`;
        reviewText += `Assessment Date: ${new Date().toLocaleDateString()}\n\n`;

        responses.forEach((response, index) => {
            reviewText += `Question ${index + 1}:\n`;
            reviewText += `Type: ${response.questionType || 'Unknown'}\n`;
            reviewText += `Topic: ${response.topic || 'Unknown'}\n`;
            reviewText += `Question: ${response.questionText || 'Not available'}\n`;
            reviewText += `Correct Answer: ${response.correctAnswer || 'Not available'}\n`;
            reviewText += `Student Answer: ${response.studentAnswer || 'No response'}\n`;
            reviewText += `Result: ${response.isCorrect ? 'CORRECT' : 'INCORRECT'}\n`;
            reviewText += `Time Spent: ${response.timeSpent || 0}ms\n\n`;
        });

        // Show in a new window
        const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
        reviewWindow.document.write(`
            <html>
                <head>
                    <title>Mathematics Assessment Review</title>
                    <style>
                        body { font-family: monospace; padding: 20px; line-height: 1.6; }
                        h1 { color: #333; }
                        .correct { color: green; }
                        .incorrect { color: red; }
                    </style>
                </head>
                <body>
                    <h1>Mathematics Assessment Review</h1>
                    <pre>${reviewText}</pre>
                    <button onclick="window.close()">Close</button>
                </body>
            </html>
        `);
    }

    /**
     * Handle show basic review
     */
    function handleShowBasicReview() {
        // Toggle back to basic view (hide detailed responses)
        const viewBtn = document.querySelector('#view-detailed-math-responses');
        const basicBtn = document.querySelector('#show-basic-math-review');

        if (viewBtn) viewBtn.style.display = 'flex';
        if (basicBtn) basicBtn.style.display = 'none';
    }

    /**
     * Add modal styles matching English modal design
     */
    function addModalStyles() {
        if (document.getElementById('math-modal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'math-modal-styles';
        styles.textContent = `
            /* Modal Overlay - matches English modal */
            .math-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                padding: 1rem;
                opacity: 0;
                transition: opacity 0.4s ease;
            }

            /* Modal Content - matches English modal */
            .math-modal-content {
                background: white;
                border-radius: 8px;
                width: 100%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.4s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                font-size: 0.875rem;
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            /* Modal Header - matches English modal */
            .math-modal-header {
                padding: 0.75rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .math-modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .math-modal-employee-title {
                font-size: 1rem;
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .math-modal-subtitle {
                font-size: 0.8rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            .math-modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .math-close-modal-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .math-close-modal-button svg {
                stroke: #1e3a8a;
            }

            .math-close-modal-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            /* Modal Body - matches English modal */
            .math-modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            /* Score Overview Section - matches English modal */
            .math-score-overview {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin-bottom: 1.5rem;
                border-left: 4px solid #1e3a8a;
            }

            .math-score-overview.qualified {
                border-left-color: #059669;
            }

            .math-score-overview.needs-improvement {
                border-left-color: #f59e0b;
            }

            .math-score-display {
                text-align: center;
                margin-bottom: 1rem;
            }

            .math-score-value {
                font-size: 2rem;
                font-weight: 600;
                color: #1e3a8a;
                line-height: 1;
            }

            .math-score-value.overridden {
                color: #dc2626;
                position: relative;
            }

            .math-score-level {
                font-size: 0.9rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0.5rem 0;
            }

            .math-score-level.overridden {
                color: #dc2626;
            }

            .override-indicator {
                font-size: 0.75rem;
                color: #dc2626;
                font-weight: 500;
                background: #fef2f2;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                margin: 0.25rem 0;
                border: 1px solid #fecaca;
                display: inline-block;
            }

            .math-score-status {
                font-size: 0.8rem;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                display: inline-block;
                background: #f0f9ff;
                color: #1e3a8a;
            }

            .math-score-status.qualified {
                background: #f0fdf4;
                color: #059669;
            }

            .math-score-status.needs-improvement {
                background: #fffbeb;
                color: #f59e0b;
            }

            .math-assessment-meta {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 1rem;
                font-size: 0.8rem;
            }

            .math-meta-item {
                text-align: center;
            }

            .math-meta-label {
                display: block;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .math-meta-value {
                font-weight: 600;
                color: #374151;
            }

            /* Analysis Section - matches English modal */
            .math-analysis-section {
                margin-bottom: 1.5rem;
            }

            .math-analysis-section h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-feedback-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .math-feedback-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-feedback-item h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-feedback-item p {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
            }

            .math-overall-feedback {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f8fafc;
                border-left: 3px solid #1e3a8a;
            }

            .math-overall-feedback h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-overall-feedback p {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
            }

            /* Course Recommendations Section - matches English modal */
            .math-course-recommendations {
                margin-bottom: 1.5rem;
            }

            .math-course-recommendations h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-recommendations-content {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-recommendations-description {
                margin-bottom: 1rem;
            }

            .course-description {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
            }

            .math-eligible-courses h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-courses-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.5rem;
                margin-bottom: 1rem;
            }

            .math-course-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem;
                background: #f8fafc;
                border-radius: 4px;
                border: 1px solid #e5e7eb;
            }

            .math-course-icon {
                font-size: 1rem;
            }

            .math-course-name {
                font-size: 0.8rem;
                color: #374151;
            }

            .math-next-steps-recommendation h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .course-next-steps {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
            }

            /* Level Progression Section */
            .math-level-progression {
                margin-bottom: 1.5rem;
            }

            .math-level-progression h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-progression-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .math-level-progress-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-level-progress-item.completed-passed {
                border-left: 4px solid #059669;
                background: #f0fdf4;
            }

            .math-level-progress-item.completed-failed {
                border-left: 4px solid #f59e0b;
                background: #fffbeb;
            }

            .math-level-progress-item.not-attempted {
                border-left: 4px solid #9ca3af;
                background: #f9fafb;
            }

            .math-level-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 0.75rem;
            }

            .math-level-info h4 {
                margin: 0 0 0.25rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-level-description {
                margin: 0;
                font-size: 0.75rem;
                color: #6b7280;
            }

            .status-badge {
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.7rem;
                font-weight: 600;
            }

            .status-badge.passed {
                background: #059669;
                color: white;
            }

            .status-badge.failed {
                background: #f59e0b;
                color: white;
            }

            .status-badge.not-attempted {
                background: #9ca3af;
                color: white;
            }

            .math-level-progress {
                margin-bottom: 0.5rem;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e5e7eb;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 0.25rem;
            }

            .progress-fill {
                height: 100%;
                transition: width 0.3s ease;
            }

            .progress-fill.passed {
                background: #059669;
            }

            .progress-fill.failed {
                background: #f59e0b;
            }

            .progress-text {
                font-size: 0.75rem;
                color: #4b5563;
                text-align: right;
            }

            .math-level-time {
                font-size: 0.75rem;
                color: #6b7280;
            }

            .time-label {
                font-weight: 500;
            }

            .math-progression-summary {
                background: #f8fafc;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 0.75rem;
                font-size: 0.8rem;
            }

            .math-progression-summary p {
                margin: 0 0 0.25rem;
                color: #4b5563;
            }

            .math-progression-summary p:last-child {
                margin-bottom: 0;
            }

            /* Topic Performance Section */
            .math-topic-performance {
                margin-bottom: 1.5rem;
            }

            .math-topic-performance h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-topics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .math-topic-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 0.75rem;
                background: #ffffff;
            }

            .topic-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;
            }

            .topic-name {
                font-size: 0.8rem;
                font-weight: 500;
                color: #374151;
            }

            .topic-level {
                font-size: 0.7rem;
                color: #6b7280;
                background: #f3f4f6;
                padding: 0.125rem 0.375rem;
                border-radius: 3px;
            }

            .topic-progress {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .topic-progress-bar {
                flex: 1;
                height: 6px;
                background: #e5e7eb;
                border-radius: 3px;
                overflow: hidden;
            }

            .topic-progress-fill {
                height: 100%;
                transition: width 0.3s ease;
            }

            .topic-progress-fill.excellent {
                background: #059669;
            }

            .topic-progress-fill.good {
                background: #f59e0b;
            }

            .topic-progress-fill.needs-work {
                background: #ef4444;
            }

            .topic-score {
                font-size: 0.75rem;
                color: #4b5563;
                font-weight: 500;
                min-width: 60px;
                text-align: right;
            }

            .topic-legend {
                display: flex;
                gap: 1rem;
                justify-content: center;
                padding: 0.5rem;
                background: #f8fafc;
                border-radius: 6px;
                font-size: 0.75rem;
            }

            .legend-item {
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
            }

            .legend-color.excellent {
                background: #059669;
            }

            .legend-color.good {
                background: #f59e0b;
            }

            .legend-color.needs-work {
                background: #ef4444;
            }

            .no-topic-data {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 1rem;
            }

            /* Time Analytics Section */
            .math-time-analytics {
                margin-bottom: 1.5rem;
            }

            .math-time-analytics h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .time-overview {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .time-summary-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem;
                background: #f8fafc;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
            }

            .time-label {
                font-size: 0.8rem;
                color: #6b7280;
            }

            .time-value {
                font-size: 0.8rem;
                font-weight: 600;
                color: #374151;
            }

            .time-breakdown {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .time-breakdown h4 {
                margin: 0 0 0.75rem;
                font-size: 0.85rem;
                color: #1e3a8a;
            }

            .time-breakdown-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0;
                border-bottom: 1px solid #f3f4f6;
                font-size: 0.8rem;
            }

            .time-breakdown-item:last-child {
                border-bottom: none;
            }

            .time-level {
                font-weight: 500;
                color: #374151;
            }

            .time-duration {
                color: #1e3a8a;
                font-weight: 600;
            }

            .time-date {
                color: #6b7280;
                font-size: 0.75rem;
            }

            .no-time-data {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                margin: 0;
            }

            /* Strengths and Improvements Section - matches English modal */
            .math-strengths-improvements {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .math-strengths-section,
            .math-improvements-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-strengths-section h4,
            .math-improvements-section h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-strengths-list,
            .math-improvements-list {
                margin: 0;
                padding-left: 1rem;
                list-style-type: disc;
            }

            .math-strengths-list li,
            .math-improvements-list li {
                margin-bottom: 0.25rem;
                color: #4b5563;
                line-height: 1.4;
                font-size: 0.8rem;
            }

            /* Next Steps Section - matches English modal */
            .math-next-steps {
                margin-bottom: 1.5rem;
            }

            .math-next-steps h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-next-steps-text {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
                padding: 1rem;
                background: #f8fafc;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
                border-left: 3px solid #1e3a8a;
            }

            /* Admin Review Section - matches English modal */
            .math-admin-review {
                margin-bottom: 1.5rem;
            }

            .math-admin-review h4 {
                margin: 0 0 0.75rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .admin-review-buttons {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 0.75rem;
                align-items: center;
                flex-wrap: wrap;
            }

            .admin-review-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 0.75rem;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
            }

            .admin-review-btn.primary {
                background: #1e3a8a;
                color: white;
            }

            .admin-review-btn.primary:hover {
                background: #1e40af;
            }

            .admin-review-btn.secondary {
                background: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .admin-review-btn.secondary:hover {
                background: #e5e7eb;
            }

            .responses-available {
                font-size: 0.75rem;
                color: #059669;
                font-weight: 500;
            }

            .responses-unavailable {
                font-size: 0.75rem;
                color: #f59e0b;
                font-weight: 500;
            }

            .admin-review-info {
                font-size: 0.75rem;
                color: #6b7280;
                line-height: 1.4;
            }

            .admin-review-info p {
                margin: 0;
            }

            /* Error Content - matches English modal */
            .math-error-content {
                text-align: center;
                padding: 2rem 1rem;
                color: #6b7280;
            }

            .math-error-icon {
                margin-bottom: 1rem;
                color: #f59e0b;
            }

            .math-error-content h3 {
                margin: 0 0 0.5rem;
                color: #374151;
                font-size: 1rem;
            }

            .math-error-content p {
                margin: 0;
                line-height: 1.4;
            }

            /* Responsive Design - matches English modal */
            @media (max-width: 768px) {
                .math-modal-content {
                    margin: 0.5rem;
                    max-height: calc(100vh - 1rem);
                    font-size: 0.8rem;
                }

                .math-modal-header {
                    padding: 0.5rem;
                }

                .math-modal-body {
                    padding: 0.75rem;
                }

                .math-feedback-grid {
                    grid-template-columns: 1fr;
                    gap: 0.5rem;
                }

                .math-courses-grid {
                    grid-template-columns: 1fr;
                }

                .math-strengths-improvements {
                    grid-template-columns: 1fr;
                    gap: 0.75rem;
                }

                .math-topics-grid {
                    grid-template-columns: 1fr;
                    gap: 0.5rem;
                }

                .time-overview {
                    grid-template-columns: 1fr;
                    gap: 0.5rem;
                }

                .topic-legend {
                    flex-direction: column;
                    gap: 0.5rem;
                    align-items: center;
                }

                .math-assessment-meta {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .admin-review-buttons {
                    flex-direction: column;
                    align-items: stretch;
                }

                .admin-review-btn {
                    justify-content: center;
                }
            }

            @media (max-width: 480px) {
                .math-modal-content {
                    margin: 0.25rem;
                    max-height: calc(100vh - 0.5rem);
                }

                .math-score-value {
                    font-size: 1.5rem;
                }

                .math-modal-employee-title {
                    font-size: 0.9rem;
                }

                .math-modal-subtitle {
                    font-size: 0.75rem;
                }
            }
        `;
        document.head.appendChild(styles);
    }

})();
