<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Successful - Skills Assess</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.13/lottie.min.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
  <style>
    body {
      background-color: #f9fafb;
      color: #121c41;
    }
    .success-container {
      max-width: 800px;
    }
    .animation-container {
      width: 200px;
      height: 200px;
      margin: 0 auto;
    }
    .button-primary {
      background-color: #1547bb;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    .button-primary:hover {
      background-color: #0d338d;
    }
    .footer-link {
      color: #1547bb;
      font-weight: 500;
      transition: color 0.2s;
    }
    .footer-link:hover {
      color: #0d338d;
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="min-h-screen flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="success-container bg-white shadow-lg rounded-lg p-8 md:p-10">
      <div class="text-center">
        <div class="animation-container" id="success-animation"></div>
        <h1 class="mt-4 text-3xl font-bold text-gray-900" id="success-title">Payment Successful!</h1>
        <p class="mt-2 text-lg text-gray-600" id="plan-details">
          Your subscription has been confirmed.
        </p>
        <div class="mt-8 space-y-6">
          <div class="border-t border-b border-gray-200 py-4 px-2">
            <h2 class="text-xl font-semibold mb-4" id="access-heading">You now have access to:</h2>
            <ul class="text-left space-y-3 ml-4" id="features-list">
              <li class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Loading your subscription details...</span>
              </li>
            </ul>
          </div>
          <div class="mt-6">
            <button id="continue-button" class="button-primary w-full flex justify-center">
              Continue to Dashboard
            </button>
          </div>
        </div>
      </div>
      <div class="mt-8 text-center text-sm text-gray-500">
        <p>Need help with your subscription? <a href="mailto:<EMAIL>" class="footer-link">Contact Support</a></p>
      </div>
    </div>
  </div>

  <script>
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
      authDomain: "barefoot-elearning-app.firebaseapp.com",
      projectId: "barefoot-elearning-app",
      databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
      storageBucket: "barefoot-elearning-app.appspot.com",
      messagingSenderId: "170819735788",
      appId: "1:170819735788:web:223af318437eb5d947d5c9"
    };

    firebase.initializeApp(firebaseConfig);
    const db = firebase.firestore();

    // Initialize animation
    const animation = lottie.loadAnimation({
      container: document.getElementById('success-animation'),
      renderer: 'svg',
      loop: false,
      autoplay: true,
      path: 'success.json' // Make sure this file exists
    });

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    const userId = urlParams.get('userId');
    const credits = urlParams.get('credits');
    const isFree = urlParams.get('free') === 'true';
    const trialDays = urlParams.get('trialDays') || 14;

    // Update UI based on subscription type and current database values
    function updateUI(subscriptionType, credits, previousCredits = 0) {
      const successTitle = document.getElementById('success-title');
      const planDetails = document.getElementById('plan-details');
      const accessHeading = document.getElementById('access-heading');
      const featuresList = document.getElementById('features-list');

      // Default features for all plans
      const commonFeatures = [
        'Downloadable Learner reporting',
        'Graphical analysis reports*',
        'Recommended courses report',
        'Access to different assessment topics'
      ];

      let planName, planFeatures;
      const newCredits = parseInt(credits);
      const hadPreviousCredits = previousCredits > 0;

      // We'll get the current credits from the database later
      // This is just a fallback calculation
      let totalCredits = previousCredits + newCredits;

      if (isFree) {
        successTitle.textContent = 'Free Trial Activated!';

        if (hadPreviousCredits) {
          planDetails.innerHTML = `Your ${trialDays}-day free trial has been activated.<br>Loading your current credit balance...`;
        } else {
          planDetails.textContent = `Your ${trialDays}-day free trial has been activated with ${newCredits} credits.`;
        }

        planName = 'Free Trial';
        planFeatures = [
          `Loading current assessment count...`,
          ...commonFeatures,
          `Full access for ${trialDays} days`
        ];
      } else {
        // For paid subscriptions
        switch (newCredits) {
          case 100:
            planName = 'Assess100';
            planFeatures = [
              hadPreviousCredits ? `Loading current assessment count...` : '100 assessments',
              '12 month access',
              ...commonFeatures
            ];
            break;
          case 250:
            planName = 'Assess250';
            planFeatures = [
              hadPreviousCredits ? `Loading current assessment count...` : '250 assessments',
              '12 month access',
              ...commonFeatures
            ];
            break;
          case 500:
            planName = 'Assess500';
            planFeatures = [
              hadPreviousCredits ? `Loading current assessment count...` : '500 assessments',
              '12 month access',
              ...commonFeatures
            ];
            break;
          default:
            planName = `Custom Plan (${newCredits} credits)`;
            planFeatures = [
              hadPreviousCredits ? `Loading current assessment count...` : `${newCredits} assessments`,
              '12 month access',
              ...commonFeatures
            ];
        }

        if (hadPreviousCredits) {
          successTitle.textContent = 'Subscription Updated!';
          planDetails.innerHTML = `Your subscription has been updated.<br>Loading your current credit balance...`;
        } else {
          successTitle.textContent = 'Payment Successful!';
          planDetails.textContent = `Your subscription has been confirmed with ${newCredits} credits.`;
        }
      }

      // Update the features list
      featuresList.innerHTML = planFeatures.map(feature => `
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span>${feature}</span>
        </li>
      `).join('');

      // Add note about reports
      featuresList.innerHTML += `
        <li class="text-xs text-gray-500 mt-4 ml-7">
          *Reports include charts and heat maps showcasing skills gaps at an organizational level.
        </li>
      `;
    }

    // Handle continue button
    document.getElementById('continue-button').addEventListener('click', () => {
      window.location.href = 'simplifieddashboard.html';
    });

    // Function to update UI with the latest credit balance
    function updateUIWithLatestCredits(user) {
      // Get the latest credit balance from Firestore
      db.collection('Admins').doc(user.email).get()
        .then(doc => {
          if (doc.exists) {
            const data = doc.data();
            const currentCredits = data.credits || 0;
            const planDetails = document.getElementById('plan-details');
            const featuresList = document.getElementById('features-list');

            // Update the plan details with the current credit balance
            if (isFree) {
              planDetails.innerHTML = `Your ${trialDays}-day free trial has been activated.<br>You now have <strong>${currentCredits} credits</strong> available.`;
            } else {
              // For paid subscriptions
              if (userId) {
                planDetails.innerHTML = `Your subscription has been updated.<br>You now have <strong>${currentCredits} credits</strong> available.`;
              } else {
                planDetails.textContent = `Your subscription has been confirmed with ${currentCredits} credits.`;
              }
            }

            // Update the first feature with the current credit count
            const firstFeature = featuresList.querySelector('li');
            if (firstFeature) {
              const featureText = firstFeature.querySelector('span');
              if (featureText) {
                featureText.textContent = `${currentCredits} total assessments`;
              }
            }
          }
        })
        .catch(error => {
          console.error('Error getting latest credit data:', error);
        });
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', () => {
      // Get authentication state
      firebase.auth().onAuthStateChanged(user => {
        if (!user) {
          // If not logged in, redirect to login
          window.location.href = 'index.html?redirect=success';
          return;
        }

        // Get the user's previous credits from Firestore
        db.collection('Admins').doc(user.email).get()
          .then(doc => {
            if (doc.exists) {
              const data = doc.data();
              const previousCredits = data.credits || 0;
              const subscriptionHistory = data.subscriptionHistory || [];
              const previousSubscription = subscriptionHistory.length > 0 ?
                subscriptionHistory[subscriptionHistory.length - 1] : null;

              if (userId) {
                // For new subscriptions, show the previous credits
                updateUI(isFree ? 'freeTrial' : 'paid', credits, previousCredits);

                // Wait a short time to allow database updates to complete, then get the latest credit balance
                setTimeout(() => {
                  updateUIWithLatestCredits(user);
                }, 2000); // 2 second delay to ensure database has been updated
              } else {
                // If no URL params, just show current subscription info
                updateUI(data.paid ? 'paid' : 'freeTrial', data.credits, 0);
              }
            } else {
              // Fallback to a generic message
              updateUI('unknown', credits || 0, 0);
            }
          })
          .catch(error => {
            console.error('Error getting subscription data:', error);
            updateUI('unknown', credits || 0, 0);
          });
      });
    });
  </script>
</body>
</html>
