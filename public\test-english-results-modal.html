<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Results Modal Test</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin: 8px;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .scenario-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-2xl font-bold text-center mb-6">English Assessment Results Modal Test</h1>
        <p class="text-gray-600 text-center mb-8">Test the updated English assessment results modal with Skills Gap Modal theme consistency</p>
        
        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 1: Advanced Level (Qualified)</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 18/21 - L2/GCSE Level - Ready for digital skills training</p>
            <button class="test-button" onclick="showTestModal('advanced')">Show Advanced Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 2: Intermediate Level</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 12/21 - L1 Level - Some improvement needed</p>
            <button class="test-button" onclick="showTestModal('intermediate')">Show Intermediate Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 3: Foundation Level</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 6/21 - Entry Level - Needs focused development</p>
            <button class="test-button" onclick="showTestModal('foundation')">Show Foundation Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 4: Enhanced Data</h3>
            <p class="text-sm text-gray-600 mb-3">Complete assessment with AI feedback and detailed analysis</p>
            <button class="test-button" onclick="showTestModal('enhanced')">Show Enhanced Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 5: Entry Level 3</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 8/21 - Entry Level 3 - Beginners pathway</p>
            <button class="test-button" onclick="showTestModal('entry-level-3')">Show Entry Level 3 Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 6: Course Recommendations Focus</h3>
            <p class="text-sm text-gray-600 mb-3">Showcase enhanced course recommendations with comprehensive data</p>
            <button class="test-button" onclick="showTestModal('course-focus')">Show Course Recommendations</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 7: Error State</h3>
            <p class="text-sm text-gray-600 mb-3">Test error handling when data is not available</p>
            <button class="test-button" onclick="showTestModal('error')">Show Error State</button>
        </div>
    </div>

    <!-- Mock Firebase for testing -->
    <script>
        // Mock Firebase db object for testing
        window.db = {
            collection: () => ({
                doc: () => ({
                    collection: () => ({
                        doc: () => ({
                            get: () => Promise.resolve({
                                exists: true,
                                data: () => getTestUserData()
                            })
                        })
                    })
                })
            })
        };

        // Mock loading overlay functions
        window.showLoadingOverlay = () => console.log('Loading...');
        window.hideLoadingOverlay = () => console.log('Loading complete');

        function getTestUserData() {
            const scenario = window.currentTestScenario || 'advanced';
            
            switch (scenario) {
                case 'advanced':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 18,
                        englishProficiencyLevel: 'L2/GCSE',
                        englishResponse: 'This is a sample response demonstrating advanced English proficiency with complex sentence structures and sophisticated vocabulary.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 1800, // 30 minutes
                        englishFeedback: {
                            grammar: 'Excellent grammar skills demonstrated with complex sentence structures and accurate usage.',
                            vocabulary: 'Strong vocabulary range with appropriate word choice and professional terminology.',
                            coherence: 'Well-organized responses with clear logical flow and effective transitions.',
                            overall: 'Outstanding English proficiency at L2/GCSE level. Ready for advanced digital skills training.'
                        },
                        englishStrengths: [
                            'Excellent written communication skills',
                            'Strong grammar and sentence structure',
                            'Professional vocabulary usage',
                            'Clear and coherent expression',
                            'Ready for advanced learning'
                        ],
                        englishImprovements: [
                            'Continue practicing advanced writing techniques',
                            'Expand professional vocabulary',
                            'Maintain current proficiency level'
                        ],
                        courseRecommendations: {
                            eligible: [
                                'Level 3 Digital Skills Course',
                                'Level 3 Health and Social Care Course',
                                'Advanced Digital Skills',
                                'Professional Communication'
                            ],
                            description: 'Excellent! You have achieved Level 2 in English proficiency.',
                            nextSteps: 'You are eligible for Level 3 Digital Skills Course or below and Level 3 Health and Social Care course. You can proceed directly to advanced level courses that match your career goals.'
                        }
                    };
                case 'intermediate':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 12,
                        englishProficiencyLevel: 'L1',
                        englishResponse: 'This response shows good basic English skills with some areas for improvement.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 1200, // 20 minutes
                        courseRecommendations: {
                            eligible: [
                                'Level 2 Health and Social Care Course',
                                'Level 2 Digital Skills and Below',
                                'Level 2 English Course'
                            ],
                            description: 'Good progress! You are at Level 1 in English proficiency.',
                            nextSteps: 'You have three course options available: Level 2 Health and Social Care course, Level 2 Digital Skills and below, or Level 2 English to further improve your English proficiency.'
                        }
                    };
                case 'foundation':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 6,
                        englishProficiencyLevel: 'Entry',
                        englishResponse: 'Basic response showing foundational English skills.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 900, // 15 minutes
                        courseRecommendations: {
                            eligible: [
                                'Beginners Course',
                                'Beginners Plus Course',
                                'Entry 3 Health & Social Care Courses'
                            ],
                            description: 'You are at Entry Level 2 in English proficiency.',
                            nextSteps: 'You can enroll in Beginners or Beginners Plus courses and Entry 3 Health & Social Care courses. These foundational courses will help you progress to higher levels of learning.'
                        }
                    };
                case 'enhanced':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 15,
                        englishProficiencyLevel: 'L1',
                        englishResponse: 'A comprehensive response showcasing various English language skills and competencies.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 2100, // 35 minutes
                        englishFeedback: {
                            grammar: 'Good grammar foundation with mostly accurate sentence construction. Some complex structures could be improved.',
                            vocabulary: 'Adequate vocabulary for everyday communication with room for expansion in professional terminology.',
                            coherence: 'Generally well-structured responses with clear main ideas. Transitions could be smoother.',
                            overall: 'Solid English proficiency at L1 level. With focused improvement, ready for digital skills training.'
                        },
                        englishStrengths: [
                            'Good basic communication skills',
                            'Adequate grammar foundation',
                            'Clear expression of main ideas',
                            'Willingness to engage with complex topics',
                            'Shows potential for improvement'
                        ],
                        englishImprovements: [
                            'Practice complex sentence structures',
                            'Expand vocabulary range',
                            'Work on coherence and organization',
                            'Improve transition phrases',
                            'Consider additional English language support'
                        ],
                        courseRecommendations: {
                            eligible: [
                                'Level 2 Health and Social Care Course',
                                'Level 2 Digital Skills and Below',
                                'Level 2 English Course'
                            ],
                            description: 'Good progress! You are at Level 1 in English proficiency.',
                            nextSteps: 'You have three course options available: Level 2 Health and Social Care course, Level 2 Digital Skills and below, or Level 2 English to further improve your English proficiency.'
                        }
                    };
                case 'entry-level-3':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 8,
                        englishProficiencyLevel: 'Entry Level 3',
                        englishResponse: 'Basic response showing Entry Level 3 English skills with potential for growth.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 1500, // 25 minutes
                        englishFeedback: {
                            grammar: 'Basic grammar understanding with simple sentence structures. Room for improvement in complex constructions.',
                            vocabulary: 'Limited vocabulary range but shows understanding of common words and phrases.',
                            coherence: 'Simple but understandable communication. Ideas are present but organization could be improved.',
                            overall: 'Entry Level 3 English proficiency. Shows foundation skills that can be built upon with appropriate support.'
                        },
                        englishStrengths: [
                            'Completed the assessment successfully',
                            'Shows basic communication skills',
                            'Demonstrates willingness to learn',
                            'Foundation skills are present'
                        ],
                        englishImprovements: [
                            'Build vocabulary through reading and practice',
                            'Practice sentence construction',
                            'Work on organizing ideas clearly',
                            'Engage with Entry Level 3 courses for structured learning'
                        ],
                        courseRecommendations: {
                            eligible: [
                                'Beginners Course',
                                'Beginners Plus Course',
                                'Level 1 English Course',
                                'Entry 3 Health & Social Care Courses'
                            ],
                            description: 'You are at Entry Level 3 in English proficiency.',
                            nextSteps: 'You are eligible for Beginners or Beginners Plus courses, Level 1 English course, and Entry 3 Health & Social Care courses. These will help build your foundation for further learning.'
                        }
                    };
                case 'course-focus':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 17,
                        englishProficiencyLevel: 'L2/GCSE',
                        englishResponse: 'Comprehensive response demonstrating strong English proficiency with excellent course recommendation potential.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 2400, // 40 minutes
                        englishFeedback: {
                            grammar: 'Excellent command of grammar with sophisticated sentence structures and accurate usage throughout.',
                            vocabulary: 'Impressive vocabulary range with professional terminology and contextually appropriate word choices.',
                            coherence: 'Exceptionally well-organized responses with seamless logical flow and effective transitional elements.',
                            overall: 'Outstanding English proficiency demonstrating readiness for advanced digital skills training and professional development.'
                        },
                        englishStrengths: [
                            'Exceptional written communication abilities',
                            'Advanced grammar and complex sentence mastery',
                            'Professional-level vocabulary usage',
                            'Superior organizational and coherence skills',
                            'Demonstrates leadership communication potential',
                            'Ready for specialized technical training'
                        ],
                        englishImprovements: [
                            'Continue developing industry-specific terminology',
                            'Explore advanced technical writing techniques',
                            'Consider mentoring others in communication skills'
                        ],
                        courseRecommendations: {
                            eligible: [
                                'Level 3 Digital Skills Course',
                                'Level 3 Health and Social Care Course',
                                'Advanced Digital Skills',
                                'Professional Communication'
                            ],
                            description: 'Excellent! You have achieved Level 2 in English proficiency.',
                            nextSteps: 'You are eligible for Level 3 Digital Skills Course or below and Level 3 Health and Social Care course. You can proceed directly to advanced level courses that match your career goals.'
                        }
                    };
                default:
                    return null;
            }
        }

        // Function to show the test modal
        function showTestModal(scenario) {
            window.currentTestScenario = scenario;
            
            if (scenario === 'error') {
                // Test error state by passing null data
                if (typeof window.EnglishResultsModal !== 'undefined') {
                    window.EnglishResultsModal.show(null, '<EMAIL>', 'Test User', 'Test Company');
                } else {
                    alert('English Results Modal script not loaded properly');
                }
                return;
            }

            const testData = getTestUserData();
            if (typeof window.EnglishResultsModal !== 'undefined') {
                window.EnglishResultsModal.show(testData, '<EMAIL>', 'Test User', 'Test Company');
            } else {
                alert('English Results Modal script not loaded properly');
            }
        }
    </script>

    <!-- Include the English Results Modal script -->
    <script src="english-results-modal.js"></script>
</body>
</html>
