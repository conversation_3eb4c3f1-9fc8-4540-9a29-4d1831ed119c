/**
 * Enhanced Mathematics Assessment Review Modal
 * Redesigned to provide meaningful data and insights for administrators
 * Based on the MATHEMATICS_ASSESSMENT_ADMIN_DASHBOARD_IMPLEMENTATION.md specifications
 */

(function() {
    'use strict';

    let isModalInitialized = false;
    let currentAssessmentData = null;
    let currentUserEmail = null;
    let currentUserCompany = null;

    // Public API
    window.EnhancedMathAssessmentModal = {
        show: showEnhancedMathModal,
        hide: hideEnhancedMathModal
    };

    /**
     * Show enhanced mathematics assessment modal
     */
    async function showEnhancedMathModal(mathData, userEmail, userName, userCompany) {
        try {
            // Show loading overlay
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Store current user info
            currentUserEmail = userEmail;
            currentUserCompany = userCompany;

            // Fetch comprehensive assessment data
            const assessmentData = await fetchComprehensiveAssessmentData(userEmail, userCompany);
            currentAssessmentData = enhanceAssessmentData(assessmentData, userEmail, userName);

            if (isModalInitialized) {
                await resetAndShowModal(userName);
                return;
            }

            // Create modal
            await createEnhancedModal(userName);
            isModalInitialized = true;

        } catch (error) {
            console.error('Error showing enhanced math assessment modal:', error);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            if (typeof showNotification === 'function') {
                showNotification('Failed to load mathematics assessment details', 'error');
            }
        }
    }

    /**
     * Hide the enhanced modal
     */
    function hideEnhancedMathModal() {
        const overlay = document.getElementById('enhanced-math-modal-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.remove();
                isModalInitialized = false;
            }, 300);
        }
    }

    /**
     * Fetch comprehensive assessment data from Firebase
     */
    async function fetchComprehensiveAssessmentData(userEmail, userCompany) {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            const userRef = db.collection('companies')
                             .doc(userCompany)
                             .collection('users')
                             .doc(userEmail);

            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            
            // Extract comprehensive mathematics data following the schema
            return {
                // Core assessment fields
                mathAssessmentCompleted: userData.mathAssessmentCompleted || false,
                mathCurrentLevel: userData.mathCurrentLevel || null,
                mathOverallScore: userData.mathOverallScore || 0,
                mathHighestLevelCompleted: userData.mathHighestLevelCompleted || null,
                mathAssessmentTimestamp: userData.mathAssessmentTimestamp || null,
                totalTimeSpentOnMath: userData.totalTimeSpentOnMath || 0,

                // Level-specific data with complete structure
                mathEntryLevel: userData.mathEntryLevel || createDefaultLevelData('Entry'),
                mathLevel1: userData.mathLevel1 || createDefaultLevelData('Level1'),
                mathGCSEPart1: userData.mathGCSEPart1 || createDefaultLevelData('GCSEPart1'),
                mathGCSEPart2: userData.mathGCSEPart2 || createDefaultLevelData('GCSEPart2'),

                // Enhanced feedback and analysis
                mathFeedback: userData.mathFeedback || {},
                mathStrengths: userData.mathStrengths || [],
                mathImprovements: userData.mathImprovements || [],
                mathPlacementRecommendation: userData.mathPlacementRecommendation || {},

                // Detailed response logging
                mathAssessmentResponses: userData.mathAssessmentResponses || {},

                // Manual overrides
                manualScoreOverride: userData.manualScoreOverride || null,
                manualLevelOverride: userData.manualLevelOverride || null,

                // User identification
                userEmail: userEmail,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                userType: userData.userType || 'student',
                studentLevel: userData.studentLevel || 'adult-learner'
            };

        } catch (error) {
            console.error('Error fetching comprehensive assessment data:', error);
            return null;
        }
    }

    /**
     * Create default level data structure
     */
    function createDefaultLevelData(level) {
        const levelSpecs = {
            'Entry': {
                maxScore: 44,
                passingScore: 24,
                questionCount: 22,
                timeLimit: 30 * 60,
                topics: {
                    arithmetic: { score: 0, maxScore: 8 },
                    fractions: { score: 0, maxScore: 6 },
                    percentages: { score: 0, maxScore: 4 },
                    basicAlgebra: { score: 0, maxScore: 6 },
                    measurement: { score: 0, maxScore: 4 },
                    dataHandling: { score: 0, maxScore: 4 }
                }
            },
            'Level1': {
                maxScore: 26,
                passingScore: 16,
                questionCount: 13,
                timeLimit: 30 * 60,
                topics: {
                    advancedArithmetic: { score: 0, maxScore: 4 },
                    fractionsDecimals: { score: 0, maxScore: 4 },
                    percentagesRatio: { score: 0, maxScore: 4 },
                    algebraicExpressions: { score: 0, maxScore: 6 },
                    geometry: { score: 0, maxScore: 4 },
                    statistics: { score: 0, maxScore: 4 }
                }
            },
            'GCSEPart1': {
                maxScore: 10,
                passingScore: 5,
                questionCount: 7,
                timeLimit: 15 * 60,
                topics: {
                    numberOperations: { score: 0, maxScore: 3 },
                    algebraicManipulation: { score: 0, maxScore: 3 },
                    geometricReasoning: { score: 0, maxScore: 2 },
                    fractionalCalculations: { score: 0, maxScore: 2 }
                }
            },
            'GCSEPart2': {
                maxScore: 20,
                passingScore: 8,
                questionCount: 10,
                timeLimit: 20 * 60,
                topics: {
                    complexCalculations: { score: 0, maxScore: 4 },
                    statisticalAnalysis: { score: 0, maxScore: 4 },
                    trigonometry: { score: 0, maxScore: 4 },
                    advancedAlgebra: { score: 0, maxScore: 4 },
                    problemSolving: { score: 0, maxScore: 4 }
                }
            }
        };

        const spec = levelSpecs[level] || levelSpecs['Entry'];
        return {
            completed: false,
            score: 0,
            passed: false,
            timeSpent: 0,
            completedAt: null,
            responses: [],
            topicBreakdown: spec.topics
        };
    }

    /**
     * Enhance assessment data with calculated insights
     */
    function enhanceAssessmentData(rawData, userEmail, userName) {
        if (!rawData) return null;

        // Calculate enhanced metrics
        const completedLevels = getCompletedLevels(rawData);
        const totalPossibleScore = calculateTotalPossibleScore(completedLevels, rawData);
        const overallPercentage = totalPossibleScore > 0 ? Math.round((rawData.mathOverallScore / totalPossibleScore) * 100) : 0;

        // Calculate time efficiency metrics
        const timeEfficiency = calculateTimeEfficiency(rawData);

        // Determine learning path recommendations
        const learningPath = determineLearningPath(rawData, completedLevels);

        return {
            ...rawData,
            // Enhanced metrics
            completedLevels,
            totalPossibleScore,
            overallPercentage,
            timeEfficiency,
            learningPath,

            // User display info
            displayName: userName,
            userEmail: userEmail
        };
    }

    /**
     * Get completed levels from assessment data
     */
    function getCompletedLevels(data) {
        const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];
        return levels.filter(level => data[level]?.completed);
    }

    /**
     * Calculate total possible score based on completed levels
     */
    function calculateTotalPossibleScore(completedLevels, data) {
        const maxScores = {
            'mathEntryLevel': 44,
            'mathLevel1': 26,
            'mathGCSEPart1': 10,
            'mathGCSEPart2': 20
        };
        
        return completedLevels.reduce((total, level) => total + maxScores[level], 0);
    }



    /**
     * Calculate time efficiency metrics
     */
    function calculateTimeEfficiency(data) {
        const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];
        const timeData = [];
        
        levels.forEach(levelKey => {
            const levelData = data[levelKey];
            if (levelData?.completed) {
                const timeSpent = levelData.timeSpent || 0;
                const score = levelData.score || 0;
                const maxScore = getLevelMaxScore(levelKey);
                
                timeData.push({
                    level: levelKey.replace('math', ''),
                    timeSpent,
                    score,
                    maxScore,
                    efficiency: timeSpent > 0 ? score / (timeSpent / 60) : 0 // points per minute
                });
            }
        });
        
        return timeData;
    }

    /**
     * Get maximum score for a level
     */
    function getLevelMaxScore(levelKey) {
        const maxScores = {
            'mathEntryLevel': 44,
            'mathLevel1': 26,
            'mathGCSEPart1': 10,
            'mathGCSEPart2': 20
        };
        return maxScores[levelKey] || 0;
    }

    /**
     * Determine learning path recommendations
     */
    function determineLearningPath(data, completedLevels) {
        const recommendations = [];
        const currentLevel = data.mathCurrentLevel;
        const highestCompleted = data.mathHighestLevelCompleted;
        
        // Analyze performance and suggest next steps
        if (completedLevels.length === 0) {
            recommendations.push({
                type: 'start',
                message: 'Begin with Entry Level mathematics assessment',
                priority: 'high'
            });
        } else {
            // Check if current level was passed
            const currentLevelData = data[`math${currentLevel}`];
            if (currentLevelData?.passed) {
                const nextLevel = getNextLevel(currentLevel);
                if (nextLevel) {
                    recommendations.push({
                        type: 'progress',
                        message: `Ready to progress to ${nextLevel}`,
                        priority: 'high'
                    });
                }
            } else if (currentLevelData?.completed && !currentLevelData.passed) {
                recommendations.push({
                    type: 'retry',
                    message: `Consider retaking ${currentLevel} with additional preparation`,
                    priority: 'medium'
                });
            }
        }
        
        return recommendations;
    }

    /**
     * Get next level in progression
     */
    function getNextLevel(currentLevel) {
        const progression = {
            'Entry': 'Level1',
            'Level1': 'GCSEPart1',
            'GCSEPart1': 'GCSEPart2',
            'GCSEPart2': null
        };
        return progression[currentLevel];
    }

    /**
     * Create enhanced modal
     */
    async function createEnhancedModal(userName) {
        // Remove existing modal if any
        const existingOverlay = document.getElementById('enhanced-math-modal-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create modal HTML
        const modalHTML = createEnhancedModalHTML(userName);

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add styles
        addEnhancedModalStyles();

        // Initialize event listeners
        const overlay = document.getElementById('enhanced-math-modal-overlay');
        initializeEnhancedEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.enhanced-math-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Reset and show modal with new data
     */
    async function resetAndShowModal(userName) {
        const overlay = document.getElementById('enhanced-math-modal-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new data
        overlay.innerHTML = createEnhancedModalContent(userName);

        // Re-initialize event listeners
        initializeEnhancedEventListeners(overlay);

        // Show modal
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.enhanced-math-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Create enhanced modal HTML structure
     */
    function createEnhancedModalHTML(userName) {
        return `
            <div id="enhanced-math-modal-overlay" class="enhanced-math-modal-overlay" data-user-email="${currentAssessmentData?.userEmail || ''}">
                ${createEnhancedModalContent(userName)}
            </div>
        `;
    }

    /**
     * Create enhanced modal content
     */
    function createEnhancedModalContent(userName) {
        if (!currentAssessmentData) {
            return createEnhancedErrorContent(userName);
        }

        return `
            <div class="enhanced-math-modal-content">
                <div class="enhanced-math-modal-header">
                    <div class="enhanced-math-modal-title-container">
                        <h2 class="enhanced-math-modal-title">${userName}</h2>
                        <h3 class="enhanced-math-modal-subtitle">Mathematics Assessment Analysis</h3>
                        <div class="enhanced-math-completion-status ${currentAssessmentData.mathAssessmentCompleted ? 'completed' : 'in-progress'}">
                            ${currentAssessmentData.mathAssessmentCompleted ? '✓ Assessment Completed' : '⏳ Assessment In Progress'}
                        </div>
                    </div>
                    <div class="enhanced-math-modal-actions">
                        <button id="close-enhanced-math-modal" class="enhanced-math-close-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="enhanced-math-modal-body">
                    ${createAssessmentOverviewSection()}
                    ${createLevelProgressionSection()}
                    ${createTimeAnalyticsSection()}
                    ${createLearningPathSection()}
                    ${createAdminActionsSection()}
                </div>
            </div>
        `;
    }

    /**
     * Create error content for enhanced modal
     */
    function createEnhancedErrorContent(userName) {
        return `
            <div class="enhanced-math-modal-content">
                <div class="enhanced-math-modal-header">
                    <div class="enhanced-math-modal-title-container">
                        <h2 class="enhanced-math-modal-title">${userName}</h2>
                        <h3 class="enhanced-math-modal-subtitle">Mathematics Assessment Analysis</h3>
                    </div>
                    <div class="enhanced-math-modal-actions">
                        <button id="close-enhanced-math-modal" class="enhanced-math-close-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="enhanced-math-modal-body">
                    <div class="enhanced-math-error-content">
                        <div class="enhanced-math-error-icon">
                            <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                        </div>
                        <h3>Assessment Data Unavailable</h3>
                        <p>Unable to load mathematics assessment data for analysis. The student may not have started the assessment yet, or there may be a technical issue.</p>
                        <div class="enhanced-math-error-actions">
                            <button onclick="location.reload()" class="enhanced-math-retry-button">
                                Refresh Page
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create assessment overview section
     */
    function createAssessmentOverviewSection() {
        const data = currentAssessmentData;
        const overallScore = data.mathOverallScore || 0;
        const totalPossible = data.totalPossibleScore || 0;
        const percentage = data.overallPercentage || 0;
        const timeSpent = data.totalTimeSpentOnMath || 0;
        const completedLevels = data.completedLevels?.length || 0;

        // Format time
        const formattedTime = timeSpent > 0 ?
            `${Math.floor(timeSpent / 3600)}h ${Math.floor((timeSpent % 3600) / 60)}m` :
            'Not recorded';

        // Format completion date
        const completionDate = data.mathAssessmentTimestamp ?
            new Date(data.mathAssessmentTimestamp.toDate ? data.mathAssessmentTimestamp.toDate() : data.mathAssessmentTimestamp)
                .toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' }) :
            'Not completed';

        // Determine performance level
        const performanceLevel = percentage >= 80 ? 'excellent' :
                                percentage >= 60 ? 'good' :
                                percentage >= 40 ? 'fair' : 'needs-improvement';

        return `
            <div class="enhanced-math-overview-section">
                <h3>Assessment Overview</h3>
                <div class="enhanced-math-overview-grid">
                    <div class="enhanced-math-overview-card primary">
                        <div class="enhanced-math-overview-icon">
                            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <div class="enhanced-math-overview-content">
                            <div class="enhanced-math-overview-value">${overallScore}/${totalPossible}</div>
                            <div class="enhanced-math-overview-label">Overall Score</div>
                            <div class="enhanced-math-overview-percentage ${performanceLevel}">${percentage}%</div>
                        </div>
                    </div>

                    <div class="enhanced-math-overview-card">
                        <div class="enhanced-math-overview-icon">
                            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <div class="enhanced-math-overview-content">
                            <div class="enhanced-math-overview-value">${completedLevels}/4</div>
                            <div class="enhanced-math-overview-label">Levels Completed</div>
                            <div class="enhanced-math-overview-detail">${data.mathHighestLevelCompleted || 'None'}</div>
                        </div>
                    </div>

                    <div class="enhanced-math-overview-card">
                        <div class="enhanced-math-overview-icon">
                            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="enhanced-math-overview-content">
                            <div class="enhanced-math-overview-value">${formattedTime}</div>
                            <div class="enhanced-math-overview-label">Time Spent</div>
                            <div class="enhanced-math-overview-detail">Total Duration</div>
                        </div>
                    </div>

                    <div class="enhanced-math-overview-card">
                        <div class="enhanced-math-overview-icon">
                            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div class="enhanced-math-overview-content">
                            <div class="enhanced-math-overview-value">${completionDate}</div>
                            <div class="enhanced-math-overview-label">Last Activity</div>
                            <div class="enhanced-math-overview-detail">${data.mathAssessmentCompleted ? 'Completed' : 'In Progress'}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create level progression section
     */
    function createLevelProgressionSection() {
        const data = currentAssessmentData;
        const levels = [
            { key: 'mathEntryLevel', name: 'Entry Level', maxScore: 44, passingScore: 24 },
            { key: 'mathLevel1', name: 'Level 1', maxScore: 26, passingScore: 16 },
            { key: 'mathGCSEPart1', name: 'GCSE Part 1', maxScore: 10, passingScore: 5 },
            { key: 'mathGCSEPart2', name: 'GCSE Part 2', maxScore: 20, passingScore: 8 }
        ];

        return `
            <div class="enhanced-math-progression-section">
                <h3>Level Progression</h3>
                <div class="enhanced-math-progression-timeline">
                    ${levels.map((level, index) => {
                        const levelData = data[level.key] || {};
                        const isCompleted = levelData.completed || false;
                        const isPassed = levelData.passed || false;
                        const score = levelData.score || 0;
                        const timeSpent = levelData.timeSpent || 0;
                        const isCurrent = data.mathCurrentLevel === level.key.replace('math', '');

                        let status = 'not-started';
                        let statusIcon = '○';
                        let statusText = 'Not Started';

                        if (isCompleted) {
                            if (isPassed) {
                                status = 'passed';
                                statusIcon = '✓';
                                statusText = 'Passed';
                            } else {
                                status = 'failed';
                                statusIcon = '✗';
                                statusText = 'Failed';
                            }
                        } else if (isCurrent) {
                            status = 'current';
                            statusIcon = '◐';
                            statusText = 'In Progress';
                        }

                        const percentage = level.maxScore > 0 ? Math.round((score / level.maxScore) * 100) : 0;
                        const formattedTime = timeSpent > 0 ? `${Math.round(timeSpent / 60)}min` : '';

                        return `
                            <div class="enhanced-math-progression-item ${status}">
                                <div class="enhanced-math-progression-indicator">
                                    <div class="enhanced-math-progression-icon">${statusIcon}</div>
                                    ${index < levels.length - 1 ? '<div class="enhanced-math-progression-connector"></div>' : ''}
                                </div>
                                <div class="enhanced-math-progression-content">
                                    <div class="enhanced-math-progression-header">
                                        <h4>${level.name}</h4>
                                        <span class="enhanced-math-progression-status ${status}">${statusText}</span>
                                    </div>
                                    ${isCompleted ? `
                                        <div class="enhanced-math-progression-details">
                                            <div class="enhanced-math-progression-score">
                                                Score: ${score}/${level.maxScore} (${percentage}%)
                                            </div>
                                            <div class="enhanced-math-progression-time">
                                                Time: ${formattedTime}
                                            </div>
                                            <div class="enhanced-math-progression-result ${isPassed ? 'passed' : 'failed'}">
                                                ${isPassed ? `Passed (≥${level.passingScore})` : `Failed (<${level.passingScore})`}
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }



    /**
     * Create time analytics section
     */
    function createTimeAnalyticsSection() {
        const data = currentAssessmentData;
        const timeEfficiency = data.timeEfficiency || [];

        if (timeEfficiency.length === 0) {
            return `
                <div class="enhanced-math-time-section">
                    <h3>Time Analytics</h3>
                    <div class="enhanced-math-no-data">
                        <p>No time analytics available. Complete assessments to see time efficiency analysis.</p>
                    </div>
                </div>
            `;
        }

        // Calculate average efficiency
        const avgEfficiency = timeEfficiency.reduce((sum, level) => sum + level.efficiency, 0) / timeEfficiency.length;

        return `
            <div class="enhanced-math-time-section">
                <h3>Time Analytics</h3>

                <div class="enhanced-math-time-summary">
                    <div class="enhanced-math-time-stat">
                        <div class="enhanced-math-time-stat-value">${avgEfficiency.toFixed(1)}</div>
                        <div class="enhanced-math-time-stat-label">Avg Points/Min</div>
                    </div>
                    <div class="enhanced-math-time-stat">
                        <div class="enhanced-math-time-stat-value">${Math.round(data.totalTimeSpentOnMath / 60)}</div>
                        <div class="enhanced-math-time-stat-label">Total Minutes</div>
                    </div>
                    <div class="enhanced-math-time-stat">
                        <div class="enhanced-math-time-stat-value">${timeEfficiency.length}</div>
                        <div class="enhanced-math-time-stat-label">Levels Timed</div>
                    </div>
                </div>

                <div class="enhanced-math-time-details">
                    ${timeEfficiency.map(level => {
                        const timeInMinutes = Math.round(level.timeSpent / 60);
                        const percentage = level.maxScore > 0 ? Math.round((level.score / level.maxScore) * 100) : 0;
                        const efficiencyRating = level.efficiency >= 2 ? 'excellent' :
                                               level.efficiency >= 1 ? 'good' :
                                               level.efficiency >= 0.5 ? 'fair' : 'slow';

                        return `
                            <div class="enhanced-math-time-item">
                                <div class="enhanced-math-time-level">${level.level}</div>
                                <div class="enhanced-math-time-metrics">
                                    <div class="enhanced-math-time-metric">
                                        <span class="enhanced-math-time-metric-label">Time:</span>
                                        <span class="enhanced-math-time-metric-value">${timeInMinutes} min</span>
                                    </div>
                                    <div class="enhanced-math-time-metric">
                                        <span class="enhanced-math-time-metric-label">Score:</span>
                                        <span class="enhanced-math-time-metric-value">${level.score}/${level.maxScore} (${percentage}%)</span>
                                    </div>
                                    <div class="enhanced-math-time-metric">
                                        <span class="enhanced-math-time-metric-label">Efficiency:</span>
                                        <span class="enhanced-math-time-metric-value ${efficiencyRating}">${level.efficiency.toFixed(1)} pts/min</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }



    /**
     * Create learning path section
     */
    function createLearningPathSection() {
        const data = currentAssessmentData;
        const learningPath = data.learningPath || [];
        const strengths = data.mathStrengths || [];
        const improvements = data.mathImprovements || [];

        return `
            <div class="enhanced-math-learning-section">
                <h3>Learning Path & Recommendations</h3>

                ${learningPath.length > 0 ? `
                    <div class="enhanced-math-recommendations">
                        <h4>Next Steps</h4>
                        ${learningPath.map(rec => `
                            <div class="enhanced-math-recommendation ${rec.priority}">
                                <div class="enhanced-math-recommendation-icon">
                                    ${rec.type === 'start' ? '🚀' : rec.type === 'progress' ? '⬆️' : '🔄'}
                                </div>
                                <div class="enhanced-math-recommendation-content">
                                    <div class="enhanced-math-recommendation-message">${rec.message}</div>
                                    <div class="enhanced-math-recommendation-priority">Priority: ${rec.priority}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}

                <div class="enhanced-math-strengths-improvements">
                    <div class="enhanced-math-strengths">
                        <h4>Identified Strengths</h4>
                        ${strengths.length > 0 ? `
                            <ul class="enhanced-math-strengths-list">
                                ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        ` : '<p class="enhanced-math-no-data">No specific strengths identified yet.</p>'}
                    </div>

                    <div class="enhanced-math-improvements">
                        <h4>Areas for Development</h4>
                        ${improvements.length > 0 ? `
                            <ul class="enhanced-math-improvements-list">
                                ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                            </ul>
                        ` : '<p class="enhanced-math-no-data">No specific improvement areas identified yet.</p>'}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create admin actions section
     */
    function createAdminActionsSection() {
        const data = currentAssessmentData;
        const hasDetailedData = data.mathAssessmentResponses &&
                               (data.mathAssessmentResponses.questionResponses?.length > 0 ||
                                Object.keys(data.mathAssessmentResponses).length > 0);

        return `
            <div class="enhanced-math-admin-section">
                <h3>Admin Actions</h3>

                <div class="enhanced-math-admin-actions">
                    <button id="export-math-data" class="enhanced-math-admin-button primary">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Export Assessment Data
                    </button>

                    <button id="view-raw-responses" class="enhanced-math-admin-button secondary" ${!hasDetailedData ? 'disabled' : ''}>
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        View Raw Responses
                    </button>

                    <button id="manual-override" class="enhanced-math-admin-button warning">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        Manual Override
                    </button>
                </div>

                <div class="enhanced-math-admin-info">
                    <div class="enhanced-math-data-status">
                        <strong>Data Availability:</strong>
                        <span class="${hasDetailedData ? 'available' : 'limited'}">
                            ${hasDetailedData ? 'Detailed response data available' : 'Limited data - basic scores only'}
                        </span>
                    </div>
                    <div class="enhanced-math-last-updated">
                        <strong>Last Updated:</strong>
                        ${data.mathAssessmentTimestamp ?
                            new Date(data.mathAssessmentTimestamp.toDate ? data.mathAssessmentTimestamp.toDate() : data.mathAssessmentTimestamp)
                                .toLocaleString('en-GB') :
                            'Not recorded'}
                    </div>
                </div>
            </div>
        `;
    }



    /**
     * Initialize event listeners for enhanced modal
     */
    function initializeEnhancedEventListeners(overlay) {
        // Close modal
        const closeButton = overlay.querySelector('#close-enhanced-math-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideEnhancedMathModal);
        }

        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target.id === 'enhanced-math-modal-overlay') {
                hideEnhancedMathModal();
            }
        });

        // Export data button
        const exportButton = overlay.querySelector('#export-math-data');
        if (exportButton) {
            exportButton.addEventListener('click', handleExportData);
        }

        // View raw responses button
        const rawResponsesButton = overlay.querySelector('#view-raw-responses');
        if (rawResponsesButton && !rawResponsesButton.disabled) {
            rawResponsesButton.addEventListener('click', handleViewRawResponses);
        }

        // Manual override button
        const overrideButton = overlay.querySelector('#manual-override');
        if (overrideButton) {
            overrideButton.addEventListener('click', handleManualOverride);
        }

        // Keyboard navigation
        document.addEventListener('keydown', handleKeyboardNavigation);
    }

    /**
     * Handle export data functionality
     */
    function handleExportData() {
        try {
            const exportData = {
                userEmail: currentAssessmentData.userEmail,
                displayName: currentAssessmentData.displayName,
                exportTimestamp: new Date().toISOString(),
                assessmentData: currentAssessmentData
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `math-assessment-${currentAssessmentData.userEmail}-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            if (typeof showNotification === 'function') {
                showNotification('Assessment data exported successfully', 'success');
            }
        } catch (error) {
            console.error('Error exporting data:', error);
            if (typeof showNotification === 'function') {
                showNotification('Failed to export assessment data', 'error');
            }
        }
    }

    /**
     * Handle view raw responses functionality
     */
    function handleViewRawResponses() {
        const responses = currentAssessmentData.mathAssessmentResponses;
        const reviewWindow = window.open('', '_blank', 'width=1000,height=700,scrollbars=yes');

        reviewWindow.document.write(`
            <html>
                <head>
                    <title>Mathematics Assessment Raw Responses - ${currentAssessmentData.displayName}</title>
                    <style>
                        body { font-family: 'Courier New', monospace; padding: 20px; line-height: 1.6; background: #f5f5f5; }
                        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                        .data-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; border-left: 4px solid #007bff; }
                        .correct { color: #28a745; font-weight: bold; }
                        .incorrect { color: #dc3545; font-weight: bold; }
                        h1, h2, h3 { color: #333; }
                        .close-btn { position: fixed; top: 20px; right: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
                    </style>
                </head>
                <body>
                    <button class="close-btn" onclick="window.close()">Close</button>
                    <div class="header">
                        <h1>Mathematics Assessment Raw Responses</h1>
                        <p><strong>Student:</strong> ${currentAssessmentData.displayName}</p>
                        <p><strong>Email:</strong> ${currentAssessmentData.userEmail}</p>
                        <p><strong>Export Time:</strong> ${new Date().toLocaleString()}</p>
                    </div>
                    <div class="data-section">
                        <h2>Raw Response Data</h2>
                        <pre>${JSON.stringify(responses, null, 2)}</pre>
                    </div>
                </body>
            </html>
        `);
    }

    /**
     * Handle manual override functionality
     */
    function handleManualOverride() {
        if (typeof showNotification === 'function') {
            showNotification('Manual override functionality coming soon', 'info');
        }
        // TODO: Implement manual override modal
    }

    /**
     * Handle keyboard navigation
     */
    function handleKeyboardNavigation(e) {
        if (e.key === 'Escape') {
            hideEnhancedMathModal();
        }
    }

    /**
     * Add enhanced modal styles
     */
    function addEnhancedModalStyles() {
        // Check if styles already added
        if (document.getElementById('enhanced-math-modal-styles')) {
            return;
        }

        const styleSheet = document.createElement('style');
        styleSheet.id = 'enhanced-math-modal-styles';
        styleSheet.textContent = `
            /* Enhanced Mathematics Assessment Modal Styles */
            .enhanced-math-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.75);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
                padding: 1rem;
            }

            .enhanced-math-modal-content {
                background: white;
                border-radius: 12px;
                width: 95%;
                max-width: 1400px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.3s ease;
            }

            .enhanced-math-modal-overlay.show .enhanced-math-modal-content {
                opacity: 1;
                transform: scale(1);
            }

            /* Modal Header */
            .enhanced-math-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 12px 12px 0 0;
            }

            .enhanced-math-modal-title-container {
                flex: 1;
            }

            .enhanced-math-modal-title {
                margin: 0 0 0.5rem;
                font-size: 1.5rem;
                font-weight: 600;
                color: white;
            }

            .enhanced-math-modal-subtitle {
                margin: 0 0 0.75rem;
                font-size: 1rem;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.9);
            }

            .enhanced-math-completion-status {
                display: inline-block;
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 500;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .enhanced-math-completion-status.completed {
                background: rgba(34, 197, 94, 0.2);
                border-color: rgba(34, 197, 94, 0.4);
            }

            .enhanced-math-completion-status.in-progress {
                background: rgba(251, 191, 36, 0.2);
                border-color: rgba(251, 191, 36, 0.4);
            }

            .enhanced-math-close-button {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 0.5rem;
                cursor: pointer;
                color: white;
                transition: all 0.2s ease;
            }

            .enhanced-math-close-button:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
            }

            /* Modal Body */
            .enhanced-math-modal-body {
                padding: 1.5rem;
                line-height: 1.6;
            }

            .enhanced-math-modal-body > div {
                margin-bottom: 2rem;
            }

            .enhanced-math-modal-body h3 {
                font-size: 1.1rem;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 1rem;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid #e5e7eb;
            }

            .enhanced-math-modal-body h4 {
                font-size: 1rem;
                font-weight: 600;
                color: #374151;
                margin: 0 0 0.75rem;
            }

            /* Overview Section */
            .enhanced-math-overview-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }

            .enhanced-math-overview-card {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 1.5rem;
                display: flex;
                align-items: center;
                gap: 1rem;
                transition: all 0.2s ease;
            }

            .enhanced-math-overview-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .enhanced-math-overview-card.primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-color: transparent;
            }

            .enhanced-math-overview-icon {
                flex-shrink: 0;
                width: 48px;
                height: 48px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }

            .enhanced-math-overview-card:not(.primary) .enhanced-math-overview-icon {
                background: #e2e8f0;
                color: #64748b;
            }

            .enhanced-math-overview-content {
                flex: 1;
            }

            .enhanced-math-overview-value {
                font-size: 1.5rem;
                font-weight: 700;
                line-height: 1;
                margin-bottom: 0.25rem;
            }

            .enhanced-math-overview-label {
                font-size: 0.9rem;
                opacity: 0.8;
                margin-bottom: 0.25rem;
            }

            .enhanced-math-overview-percentage {
                font-size: 0.8rem;
                font-weight: 600;
                padding: 0.25rem 0.5rem;
                border-radius: 12px;
                display: inline-block;
            }

            .enhanced-math-overview-percentage.excellent {
                background: rgba(34, 197, 94, 0.1);
                color: #16a34a;
            }

            .enhanced-math-overview-percentage.good {
                background: rgba(59, 130, 246, 0.1);
                color: #2563eb;
            }

            .enhanced-math-overview-percentage.fair {
                background: rgba(251, 191, 36, 0.1);
                color: #d97706;
            }

            .enhanced-math-overview-percentage.needs-improvement {
                background: rgba(239, 68, 68, 0.1);
                color: #dc2626;
            }

            .enhanced-math-overview-detail {
                font-size: 0.8rem;
                opacity: 0.7;
            }

            /* Level Progression Section */
            .enhanced-math-progression-timeline {
                position: relative;
            }

            .enhanced-math-progression-item {
                display: flex;
                align-items: flex-start;
                gap: 1rem;
                margin-bottom: 1.5rem;
                position: relative;
            }

            .enhanced-math-progression-indicator {
                position: relative;
                flex-shrink: 0;
            }

            .enhanced-math-progression-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                font-weight: bold;
                border: 3px solid;
                background: white;
            }

            .enhanced-math-progression-item.not-started .enhanced-math-progression-icon {
                border-color: #d1d5db;
                color: #9ca3af;
            }

            .enhanced-math-progression-item.current .enhanced-math-progression-icon {
                border-color: #3b82f6;
                color: #3b82f6;
                background: #eff6ff;
            }

            .enhanced-math-progression-item.passed .enhanced-math-progression-icon {
                border-color: #10b981;
                color: white;
                background: #10b981;
            }

            .enhanced-math-progression-item.failed .enhanced-math-progression-icon {
                border-color: #ef4444;
                color: white;
                background: #ef4444;
            }

            .enhanced-math-progression-connector {
                position: absolute;
                top: 40px;
                left: 50%;
                transform: translateX(-50%);
                width: 2px;
                height: 40px;
                background: #e5e7eb;
            }

            .enhanced-math-progression-item.passed .enhanced-math-progression-connector {
                background: #10b981;
            }

            .enhanced-math-progression-content {
                flex: 1;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 1rem;
            }

            .enhanced-math-progression-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;
            }

            .enhanced-math-progression-header h4 {
                margin: 0;
                font-size: 1rem;
                color: #1f2937;
            }

            .enhanced-math-progression-status {
                padding: 0.25rem 0.75rem;
                border-radius: 12px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
            }

            .enhanced-math-progression-status.not-started {
                background: #f3f4f6;
                color: #6b7280;
            }

            .enhanced-math-progression-status.current {
                background: #dbeafe;
                color: #1d4ed8;
            }

            .enhanced-math-progression-status.passed {
                background: #d1fae5;
                color: #065f46;
            }

            .enhanced-math-progression-status.failed {
                background: #fee2e2;
                color: #991b1b;
            }

            .enhanced-math-progression-details {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 0.5rem;
                font-size: 0.85rem;
            }

            .enhanced-math-progression-result.passed {
                color: #059669;
                font-weight: 600;
            }

            .enhanced-math-progression-result.failed {
                color: #dc2626;
                font-weight: 600;
            }



            /* Time Analytics Section */
            .enhanced-math-time-summary {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .enhanced-math-time-stat {
                text-align: center;
                padding: 1rem;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }

            .enhanced-math-time-stat-value {
                font-size: 1.5rem;
                font-weight: 700;
                color: #1f2937;
                line-height: 1;
            }

            .enhanced-math-time-stat-label {
                font-size: 0.8rem;
                color: #6b7280;
                margin-top: 0.25rem;
            }

            .enhanced-math-time-details {
                display: grid;
                gap: 0.75rem;
            }

            .enhanced-math-time-item {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 1rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .enhanced-math-time-level {
                font-weight: 600;
                color: #1f2937;
                min-width: 100px;
            }

            .enhanced-math-time-metrics {
                display: flex;
                gap: 1.5rem;
                align-items: center;
            }

            .enhanced-math-time-metric {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .enhanced-math-time-metric-label {
                font-size: 0.75rem;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .enhanced-math-time-metric-value {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1f2937;
            }

            .enhanced-math-time-metric-value.excellent {
                color: #059669;
            }

            .enhanced-math-time-metric-value.good {
                color: #2563eb;
            }

            .enhanced-math-time-metric-value.fair {
                color: #d97706;
            }

            .enhanced-math-time-metric-value.slow {
                color: #dc2626;
            }



            /* Learning Path Section */
            .enhanced-math-recommendations {
                margin-bottom: 1.5rem;
            }

            .enhanced-math-recommendation {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem;
                border-radius: 8px;
                margin-bottom: 0.75rem;
                border-left: 4px solid;
            }

            .enhanced-math-recommendation.high {
                background: #fef2f2;
                border-left-color: #ef4444;
            }

            .enhanced-math-recommendation.medium {
                background: #fffbeb;
                border-left-color: #f59e0b;
            }

            .enhanced-math-recommendation.low {
                background: #f0fdf4;
                border-left-color: #22c55e;
            }

            .enhanced-math-recommendation-icon {
                font-size: 1.5rem;
                flex-shrink: 0;
            }

            .enhanced-math-recommendation-content {
                flex: 1;
            }

            .enhanced-math-recommendation-message {
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 0.25rem;
            }

            .enhanced-math-recommendation-priority {
                font-size: 0.8rem;
                color: #6b7280;
                text-transform: uppercase;
                font-weight: 500;
            }

            .enhanced-math-strengths-improvements {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1.5rem;
            }

            .enhanced-math-strengths,
            .enhanced-math-improvements {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 1rem;
            }

            .enhanced-math-strengths {
                border-left: 4px solid #22c55e;
            }

            .enhanced-math-improvements {
                border-left: 4px solid #f59e0b;
            }

            .enhanced-math-strengths-list,
            .enhanced-math-improvements-list {
                margin: 0;
                padding-left: 1.25rem;
                list-style-type: disc;
            }

            .enhanced-math-strengths-list li,
            .enhanced-math-improvements-list li {
                margin-bottom: 0.5rem;
                color: #4b5563;
                line-height: 1.5;
            }

            /* Admin Actions Section */
            .enhanced-math-admin-actions {
                display: flex;
                gap: 0.75rem;
                margin-bottom: 1.5rem;
                flex-wrap: wrap;
            }

            .enhanced-math-admin-button {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1rem;
                border-radius: 6px;
                font-size: 0.9rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
                text-decoration: none;
            }

            .enhanced-math-admin-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .enhanced-math-admin-button.primary {
                background: #3b82f6;
                color: white;
            }

            .enhanced-math-admin-button.primary:hover:not(:disabled) {
                background: #2563eb;
                transform: translateY(-1px);
            }

            .enhanced-math-admin-button.secondary {
                background: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .enhanced-math-admin-button.secondary:hover:not(:disabled) {
                background: #e5e7eb;
                transform: translateY(-1px);
            }

            .enhanced-math-admin-button.warning {
                background: #f59e0b;
                color: white;
            }

            .enhanced-math-admin-button.warning:hover:not(:disabled) {
                background: #d97706;
                transform: translateY(-1px);
            }

            .enhanced-math-admin-info {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 1rem;
                font-size: 0.85rem;
            }

            .enhanced-math-admin-info > div {
                margin-bottom: 0.5rem;
            }

            .enhanced-math-admin-info > div:last-child {
                margin-bottom: 0;
            }

            .enhanced-math-data-status .available {
                color: #059669;
                font-weight: 600;
            }

            .enhanced-math-data-status .limited {
                color: #d97706;
                font-weight: 600;
            }

            /* Error Content */
            .enhanced-math-error-content {
                text-align: center;
                padding: 3rem 2rem;
                color: #6b7280;
            }

            .enhanced-math-error-icon {
                margin-bottom: 1.5rem;
                color: #f59e0b;
            }

            .enhanced-math-error-content h3 {
                margin: 0 0 1rem;
                color: #374151;
                font-size: 1.25rem;
            }

            .enhanced-math-error-content p {
                margin: 0 0 1.5rem;
                line-height: 1.6;
                max-width: 500px;
                margin-left: auto;
                margin-right: auto;
            }

            .enhanced-math-error-actions {
                display: flex;
                justify-content: center;
                gap: 1rem;
            }

            .enhanced-math-retry-button {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 6px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .enhanced-math-retry-button:hover {
                background: #2563eb;
            }

            /* No Data States */
            .enhanced-math-no-data {
                text-align: center;
                padding: 2rem;
                color: #6b7280;
                background: #f9fafb;
                border: 1px dashed #d1d5db;
                border-radius: 8px;
            }

            .enhanced-math-no-data p {
                margin: 0 0 0.5rem;
            }

            .enhanced-math-no-data ul {
                text-align: left;
                display: inline-block;
                margin: 0;
                padding-left: 1.25rem;
            }

            .enhanced-math-no-data li {
                margin-bottom: 0.25rem;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .enhanced-math-modal-content {
                    width: 98%;
                    max-height: 95vh;
                }

                .enhanced-math-modal-header {
                    padding: 1rem;
                }

                .enhanced-math-modal-body {
                    padding: 1rem;
                }

                .enhanced-math-overview-grid {
                    grid-template-columns: 1fr;
                }

                .enhanced-math-strengths-improvements {
                    grid-template-columns: 1fr;
                }

                .enhanced-math-admin-actions {
                    flex-direction: column;
                }

                .enhanced-math-time-metrics {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .enhanced-math-progression-details {
                    grid-template-columns: 1fr;
                }
            }

            @media (max-width: 480px) {
                .enhanced-math-modal-overlay {
                    padding: 0.5rem;
                }

                .enhanced-math-topic-summary,
                .enhanced-math-time-summary,
                .enhanced-math-questions-summary {
                    grid-template-columns: repeat(2, 1fr);
                }

                .enhanced-math-topic-header,
                .enhanced-math-question-type-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }
            }
        `;
        document.head.appendChild(styleSheet);
    }

})();
