/**
 * English Assessment Results Modal
 * Professional modal for displaying detailed English assessment analysis
 * Matches skills gap modal dimensions and styling
 */

(function() {
    'use strict';

    let isModalInitialized = false;
    let currentEnglishData = null;

    // Public API
    window.EnglishResultsModal = {
        show: showEnglishResultsModal,
        hide: hideModal
    };

    /**
     * Show English assessment results modal
     * @param {Object} englishData - English assessment data
     * @param {string} userEmail - User's email
     * @param {string} userName - User's name
     * @param {string} userCompany - Company ID
     */
    async function showEnglishResultsModal(englishData, userEmail, userName, userCompany) {
        try {
            // Show loading overlay immediately
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Fetch enhanced English assessment data
            const enhancedData = await fetchEnhancedEnglishData(userEmail, userCompany);
            currentEnglishData = enhancedData || englishData;

            if (isModalInitialized) {
                await resetAndShowModal(userName);
                return;
            }

            // Create modal if it doesn't exist
            await createModal(userName);
            isModalInitialized = true;

        } catch (error) {
            console.error('Error showing English results modal:', error);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            showErrorMessage('Failed to load English assessment results');
        }
    }

    /**
     * Fetch enhanced English assessment data with detailed analysis
     */
    async function fetchEnhancedEnglishData(userEmail, userCompany) {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            const userRef = db.collection('companies')
                             .doc(userCompany)
                             .collection('users')
                             .doc(userEmail);

            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            
            // Build comprehensive English assessment data
            const englishData = {
                // User identification
                userEmail: userEmail,

                // Core assessment fields
                englishAssessmentCompleted: userData.englishAssessmentCompleted || false,
                englishProficiencyScore: userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0,
                englishProficiencyLevel: userData.englishProficiencyLevel || userData.englishAssessment?.level || 'Entry',
                englishResponse: userData.englishResponse || '',
                englishAssessmentTimestamp: userData.englishAssessmentTimestamp || userData.englishAssessment?.timestamp || null,
                timeSpentOnEnglish: userData.timeSpentOnEnglish || 0,

                // Enhanced analysis fields with fallbacks
                englishFeedback: userData.englishFeedback || getDefaultFeedback(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0),
                englishStrengths: userData.englishStrengths || getDefaultStrengths(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0),
                englishImprovements: userData.englishImprovements || getDefaultImprovements(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0),

                // Course recommendations integration
                courseRecommendations: userData.courseRecommendations || getDefaultCourseRecommendations(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0, userData.englishProficiencyLevel || userData.englishAssessment?.level || 'Entry'),

                // Response transparency data for admin review
                englishAssessmentResponses: userData.englishAssessmentResponses || null,
                hasDetailedResponses: !!(userData.englishAssessmentResponses?.preliminaryQuestions || userData.englishAssessmentResponses?.essayResponse),

                // Manual override tracking
                manualScoreOverride: userData.manualScoreOverride || null,
                manualLevelOverride: userData.manualLevelOverride || null,
                overrideHistory: userData.overrideHistory || [],

                // Legacy support
                totalPoints: userData.englishAssessment?.totalPoints || userData.englishProficiencyScore || 0,
                level: userData.englishAssessment?.level || userData.englishProficiencyLevel || 'Entry',
                completed: userData.englishAssessmentCompleted || false
            };

            return englishData;

        } catch (error) {
            console.error('Error fetching enhanced English data:', error);
            return null;
        }
    }

    /**
     * Generate default feedback based on score
     */
    function getDefaultFeedback(score) {
        if (score >= 16) {
            return {
                grammar: 'Excellent grammar skills demonstrated with complex sentence structures and accurate usage.',
                vocabulary: 'Strong vocabulary range with appropriate word choice and professional terminology.',
                coherence: 'Well-organized responses with clear logical flow and effective transitions.',
                overall: 'Outstanding English proficiency at L2/GCSE level. Ready for advanced digital skills training.'
            };
        } else if (score >= 10) {
            return {
                grammar: 'Good grammar foundation with mostly accurate sentence construction.',
                vocabulary: 'Adequate vocabulary for everyday communication with room for expansion.',
                coherence: 'Generally well-structured responses with clear main ideas.',
                overall: 'Solid English proficiency at L1 level. Some areas for improvement before advanced training.'
            };
        } else {
            return {
                grammar: 'Basic grammar understanding with opportunities for improvement in sentence structure.',
                vocabulary: 'Foundational vocabulary that would benefit from expansion and development.',
                coherence: 'Simple but understandable communication with potential for better organization.',
                overall: 'Entry level English proficiency. Focused language development recommended.'
            };
        }
    }

    /**
     * Generate default strengths based on score
     */
    function getDefaultStrengths(score) {
        if (score >= 16) {
            return [
                'Excellent written communication skills',
                'Strong grammar and sentence structure',
                'Professional vocabulary usage',
                'Clear and coherent expression',
                'Ready for advanced learning'
            ];
        } else if (score >= 10) {
            return [
                'Good basic communication skills',
                'Adequate grammar foundation',
                'Clear expression of ideas',
                'Willingness to engage with assessment'
            ];
        } else {
            return [
                'Completed the assessment',
                'Basic communication attempted',
                'Foundation for improvement established'
            ];
        }
    }

    /**
     * Generate default improvements based on score
     */
    function getDefaultImprovements(score) {
        if (score >= 16) {
            return [
                'Continue practicing advanced writing techniques',
                'Expand professional vocabulary',
                'Maintain current proficiency level'
            ];
        } else if (score >= 10) {
            return [
                'Practice complex sentence structures',
                'Expand vocabulary range',
                'Work on coherence and organization',
                'Consider additional English language support'
            ];
        } else {
            return [
                'Focus on basic grammar rules',
                'Build foundational vocabulary',
                'Practice sentence construction',
                'Consider English language courses',
                'Regular reading and writing practice'
            ];
        }
    }

    /**
     * Generate default course recommendations based on score and level
     * Following the specific course eligibility pathways
     */
    function getDefaultCourseRecommendations(score, level) {
        // Level 2/GCSE (Score >= 16) - Achieved Level 2 in English
        if (score >= 16 || level === 'L2/GCSE') {
            return {
                eligible: [
                    'Level 3 Digital Skills Course',
                    'Level 3 Health and Social Care Course',
                    'Advanced Digital Skills',
                    'Professional Communication'
                ],
                description: 'Excellent! You have achieved Level 2 in English proficiency.',
                nextSteps: 'You are eligible for Level 3 Digital Skills Course or below and Level 3 Health and Social Care course. You can proceed directly to advanced level courses that match your career goals.'
            };
        }
        // Level 1 (Score 10-15) - At Level 1 in English
        else if (score >= 10 || level === 'L1') {
            return {
                eligible: [
                    'Level 2 Health and Social Care Course',
                    'Level 2 Digital Skills and Below',
                    'Level 2 English Course'
                ],
                description: 'Good progress! You are at Level 1 in English proficiency.',
                nextSteps: 'You have three course options available: Level 2 Health and Social Care course, Level 2 Digital Skills and below, or Level 2 English to further improve your English proficiency.'
            };
        }
        // Entry Level (Score 0-9) - Foundation building
        else {
            return {
                eligible: [
                    'Beginners Course',
                    'Beginners Plus Course',
                    'Entry Level Health & Social Care Courses'
                ],
                description: 'You are at Entry Level in English proficiency.',
                nextSteps: 'This learner is at Entry Level and can enroll in Beginners or Beginners Plus courses and Entry Level Health & Social Care courses to build foundational skills.'
            };
        }
    }

    /**
     * Create and show the modal
     */
    async function createModal(userName) {
        // Remove existing modal if any
        const existingOverlay = document.getElementById('english-results-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create modal HTML
        const modalHTML = createModalHTML(userName);

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add styles if not already added
        addModalStyles();

        // Initialize event listeners
        const overlay = document.getElementById('english-results-overlay');
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in with skills gap modal style
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.english-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Reset and show existing modal
     */
    async function resetAndShowModal(userName) {
        const overlay = document.getElementById('english-results-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new data
        overlay.innerHTML = createModalContent(userName);

        // Re-initialize event listeners
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in with skills gap modal style
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.english-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Create modal HTML structure
     */
    function createModalHTML(userName) {
        return `
            <div id="english-results-overlay" class="english-modal-overlay" data-user-email="${currentEnglishData?.userEmail || ''}">
                ${createModalContent(userName)}
            </div>
        `;
    }

    /**
     * Create modal content
     */
    function createModalContent(userName) {
        if (!currentEnglishData) {
            return createErrorContent();
        }

        // Determine display values based on overrides
        const originalScore = currentEnglishData.englishProficiencyScore || currentEnglishData.totalPoints || 0;
        const originalLevel = currentEnglishData.englishProficiencyLevel || currentEnglishData.level || 'Entry';
        const hasScoreOverride = currentEnglishData.manualScoreOverride;
        const hasLevelOverride = currentEnglishData.manualLevelOverride;

        // Use overridden values if available
        const displayScore = hasScoreOverride ? currentEnglishData.manualScoreOverride.newScore : originalScore;
        const displayLevel = hasScoreOverride ?
            (typeof window.calculateLevelFromScore === 'function' ?
                window.calculateLevelFromScore(currentEnglishData.manualScoreOverride.newScore) :
                currentEnglishData.manualScoreOverride.calculatedLevel) :
            (hasLevelOverride ? currentEnglishData.manualLevelOverride.newLevel : originalLevel);

        const isQualified = displayScore >= 16;
        const score = displayScore;
        const level = displayLevel;
        const feedback = currentEnglishData.englishFeedback || {};
        const strengths = currentEnglishData.englishStrengths || [];
        const improvements = currentEnglishData.englishImprovements || [];

        // Use updated course recommendations if score override is applied
        let courseRecommendations;
        if (hasScoreOverride) {
            // Get updated course recommendations based on overridden score
            courseRecommendations = typeof window.getUpdatedCourseRecommendations === 'function' ?
                window.getUpdatedCourseRecommendations(displayScore, displayLevel) :
                getDefaultCourseRecommendations(displayScore, displayLevel);
        } else if (hasLevelOverride) {
            // Get course recommendations based on overridden level
            courseRecommendations = getDefaultCourseRecommendations(originalScore, displayLevel);
        } else {
            // Use original course recommendations
            courseRecommendations = currentEnglishData.courseRecommendations || getDefaultCourseRecommendations(originalScore, originalLevel);
        }

        const timeSpent = currentEnglishData.timeSpentOnEnglish || 0;
        const timestamp = currentEnglishData.englishAssessmentTimestamp || currentEnglishData.timestamp;

        // Format timestamp
        const formattedDate = timestamp ? new Date(timestamp.toDate ? timestamp.toDate() : timestamp).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Date not available';

        // Format time spent
        const formattedTime = timeSpent > 0 ? `${Math.round(timeSpent / 60)} minutes` : 'Time not recorded';

        return `
            <div class="english-modal-content">
                <div class="english-modal-header">
                    <div class="english-modal-title-container">
                        <h2 class="english-modal-employee-title">${userName}</h2>
                        <h3 class="english-modal-subtitle">English Assessment Results</h3>
                    </div>
                    <div class="english-modal-actions">
                        <button id="close-english-modal" class="english-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="english-modal-body">
                    <!-- Score Overview Section -->
                    <div class="english-score-overview ${isQualified ? 'qualified' : 'needs-improvement'}">
                        <div class="english-score-display">
                            <div class="english-score-value ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${score}/21</div>
                            <div class="english-score-level ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${level} Level</div>
                            ${hasScoreOverride ? '<div class="override-indicator">Score Override Applied</div>' : ''}
                            ${hasLevelOverride && !hasScoreOverride ? '<div class="override-indicator">Level Override Applied</div>' : ''}
                            <div class="english-score-status ${isQualified ? 'qualified' : 'needs-improvement'}">
                                ${isQualified ? 'Qualified for Digital Skills Training' : 'Additional English Support Recommended'}
                            </div>
                        </div>
                        <div class="english-assessment-meta">
                            <div class="english-meta-item">
                                <span class="english-meta-label">Completed:</span>
                                <span class="english-meta-value">${formattedDate}</span>
                            </div>
                            <div class="english-meta-item">
                                <span class="english-meta-label">Time Spent:</span>
                                <span class="english-meta-value">${formattedTime}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Analysis Section -->
                    <div class="english-analysis-section">
                        <h3>Performance Analysis</h3>
                        <div class="english-feedback-grid">
                            <div class="english-feedback-item">
                                <h4>Grammar & Structure</h4>
                                <p>${feedback.grammar || 'Assessment completed'}</p>
                            </div>
                            <div class="english-feedback-item">
                                <h4>Vocabulary & Usage</h4>
                                <p>${feedback.vocabulary || 'Vocabulary evaluated'}</p>
                            </div>
                            <div class="english-feedback-item">
                                <h4>Organization & Coherence</h4>
                                <p>${feedback.coherence || 'Structure assessed'}</p>
                            </div>
                        </div>
                        <div class="english-overall-feedback">
                            <h4>Overall Assessment</h4>
                            <p>${feedback.overall || 'Assessment completed successfully'}</p>
                        </div>
                    </div>

                    <!-- Course Recommendations Section -->
                    <div class="english-course-recommendations">
                        <h3>Course Recommendations</h3>
                        <div class="english-recommendations-content">
                            <div class="english-recommendations-description">
                                <p class="course-description">${courseRecommendations.description || 'Course recommendations based on assessment results.'}</p>
                            </div>
                            <div class="english-eligible-courses">
                                <h4>Eligible Courses</h4>
                                <div class="english-courses-grid eligible-courses">
                                    ${(courseRecommendations.eligible || []).map(course => `
                                        <div class="english-course-item">
                                            <span class="english-course-icon">📚</span>
                                            <span class="english-course-name">${course}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="english-next-steps-recommendation">
                                <h4>Next Steps</h4>
                                <p class="course-next-steps">${courseRecommendations.nextSteps || 'Contact your administrator for course enrollment information.'}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Strengths and Improvements Section -->
                    <div class="english-strengths-improvements">
                        <div class="english-strengths-section">
                            <h4>Identified Strengths</h4>
                            <ul class="english-strengths-list">
                                ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="english-improvements-section">
                            <h4>Areas for Development</h4>
                            <ul class="english-improvements-list">
                                ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                            </ul>
                        </div>
                    </div>

                    <!-- Next Steps Section -->
                    <div class="english-next-steps">
                        <h4>Recommended Next Steps</h4>
                        <p class="english-next-steps-text">
                            ${getNextStepsMessage(isQualified, score)}
                        </p>
                    </div>

                    <!-- Admin Review Section -->
                    <div class="english-admin-review">
                        <h4>Admin Review Tools</h4>
                        <div class="admin-review-buttons">
                            <button id="view-detailed-responses" class="admin-review-btn primary">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                View Detailed Responses
                            </button>
                            <button id="show-basic-review" class="admin-review-btn secondary" style="display: none;">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Show Basic Review
                            </button>
                            ${currentEnglishData.hasDetailedResponses ? `
                                <span class="responses-available">✓ Detailed responses available</span>
                            ` : `
                                <span class="responses-unavailable">⚠ Limited response data</span>
                            `}
                        </div>
                        <div class="admin-review-info">
                            <p>Use these tools to manually review student responses and override assessment levels if needed.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create error content when data is not available
     */
    function createErrorContent() {
        return `
            <div class="english-modal-content">
                <div class="english-modal-header">
                    <div class="english-modal-title-container">
                        <h2 class="english-modal-employee-title">English Assessment Results</h2>
                        <h3 class="english-modal-subtitle">Data Not Available</h3>
                    </div>
                    <div class="english-modal-actions">
                        <button id="close-english-modal" class="english-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="english-modal-body">
                    <div class="english-error-state">
                        <div class="english-error-icon">⚠️</div>
                        <h3>Unable to Load Assessment Data</h3>
                        <p>We're having trouble loading the English assessment information for this user.</p>
                        <button onclick="location.reload()" class="english-retry-button">
                            Refresh Page
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get next steps message based on qualification status and specific course pathways
     */
    function getNextStepsMessage(isQualified, score) {
        // Level 2/GCSE (Score >= 16) - Achieved Level 2 in English
        if (isQualified || score >= 16) {
            return 'Excellent work! This learner has achieved Level 2 in English and is eligible for Level 3 Digital Skills Course or below and Level 3 Health and Social Care course.';
        }
        // Level 1 (Score 10-15) - At Level 1 in English
        else if (score >= 10) {
            return 'This learner is at Level 1 in English and has three course options: Level 2 Health and Social Care course, Level 2 Digital Skills and below, or Level 2 English to further improve English proficiency.';
        }
        // Entry Level 3 (Score 6-9)
        else if (score >= 6) {
            return 'This learner is at Entry Level 3 and is eligible for Beginners or Beginners Plus courses, Level 1 English course, and Entry 3 Health & Social Care courses.';
        }
        // Entry Level 2 (Score 0-5)
        else {
            return 'This learner is at Entry Level 2 and can enroll in Beginners or Beginners Plus courses and Entry 3 Health & Social Care courses to build foundational skills.';
        }
    }

    /**
     * Initialize event listeners for the modal
     */
    function initializeEventListeners(overlay) {
        const closeButton = overlay.querySelector('#close-english-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        const viewResponsesButton = overlay.querySelector('#view-detailed-responses');
        if (viewResponsesButton) {
            viewResponsesButton.addEventListener('click', handleViewDetailedResponses);
        }

        const basicReviewButton = overlay.querySelector('#show-basic-review');
        if (basicReviewButton) {
            basicReviewButton.addEventListener('click', handleShowBasicReview);
        }

        overlay.addEventListener('click', overlayClickHandler);
    }

    /**
     * Handle view detailed responses button click
     */
    function handleViewDetailedResponses() {
        // Get current user info from the modal context
        const userEmail = currentEnglishData?.userEmail || '<EMAIL>';
        const userName = document.querySelector('.english-modal-employee-title')?.textContent || 'Test User';
        const userCompany = 'Birmingham'; // Default company - in real implementation, this would come from context

        // Check if the review modal is available
        if (typeof window.EnglishAssessmentReviewModal === 'undefined') {
            console.error('English Assessment Review Modal not loaded');

            // Try to load the script dynamically
            const script = document.createElement('script');
            script.src = 'english-assessment-review-modal.js';
            script.onload = () => {
                console.log('English Assessment Review Modal loaded dynamically');
                // Retry the function call
                setTimeout(() => handleViewDetailedResponses(), 100);
            };
            script.onerror = () => {
                console.error('Failed to load English Assessment Review Modal script');

                // Show basic review button as fallback
                const detailedButton = document.getElementById('view-detailed-responses');
                const basicButton = document.getElementById('show-basic-review');
                if (detailedButton && basicButton) {
                    detailedButton.style.display = 'none';
                    basicButton.style.display = 'inline-flex';
                }

                if (typeof showNotification === 'function') {
                    showNotification('Using basic review mode. Detailed review unavailable.', 'warning');
                } else {
                    alert('Detailed review unavailable. Using basic review mode.');
                }
            };
            document.head.appendChild(script);

            // Show loading message
            if (typeof showNotification === 'function') {
                showNotification('Loading review feature...', 'info');
            }
            return;
        }

        // Show the detailed review modal
        try {
            window.EnglishAssessmentReviewModal.show(userEmail, userName, userCompany);
        } catch (error) {
            console.error('Error showing review modal:', error);
            if (typeof showNotification === 'function') {
                showNotification('Failed to open detailed review', 'error');
            } else {
                alert('Failed to open detailed review: ' + error.message);
            }
        }
    }

    /**
     * Handle basic review display when detailed modal isn't available
     */
    function handleShowBasicReview() {
        const responses = currentEnglishData?.englishAssessmentResponses;
        if (!responses) {
            alert('No detailed response data available for this assessment.');
            return;
        }

        let reviewText = 'ENGLISH ASSESSMENT REVIEW\n\n';
        reviewText += `User: ${currentEnglishData.userEmail || 'Unknown'}\n`;
        reviewText += `Score: ${currentEnglishData.englishProficiencyScore || 0}/21\n`;
        reviewText += `Level: ${currentEnglishData.englishProficiencyLevel || 'Unknown'}\n\n`;

        if (responses.preliminaryQuestions && responses.preliminaryQuestions.length > 0) {
            reviewText += 'PRELIMINARY QUESTIONS:\n';
            responses.preliminaryQuestions.forEach((q, index) => {
                const isCorrect = q.studentResponse && q.correctAnswer &&
                                q.studentResponse.toLowerCase().trim() === q.correctAnswer.toLowerCase().trim();
                reviewText += `Q${index + 1}: ${q.questionText}\n`;
                reviewText += `Expected: "${q.correctAnswer}"\n`;
                reviewText += `Student: "${q.studentResponse}" ${isCorrect ? '✓' : '✗'}\n\n`;
            });
        }

        if (responses.essayResponse) {
            reviewText += 'ESSAY RESPONSE:\n';
            reviewText += `Prompt: ${responses.essayResponse.prompt}\n\n`;
            reviewText += `Response (${responses.essayResponse.wordCount || 0} words):\n`;
            reviewText += `${responses.essayResponse.response}\n\n`;
        }

        // Show in a simple modal or alert
        const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
        reviewWindow.document.write(`
            <html>
                <head>
                    <title>English Assessment Review</title>
                    <style>
                        body { font-family: monospace; padding: 20px; line-height: 1.6; }
                        h1 { color: #333; }
                        .correct { color: green; }
                        .incorrect { color: red; }
                    </style>
                </head>
                <body>
                    <h1>English Assessment Review</h1>
                    <pre>${reviewText}</pre>
                    <button onclick="window.close()">Close</button>
                </body>
            </html>
        `);
    }

    /**
     * Handle overlay click to close modal
     */
    function overlayClickHandler(event) {
        if (event.target.id === 'english-results-overlay') {
            hideModal();
        }
    }

    /**
     * Hide the modal
     */
    function hideModal() {
        const overlay = document.getElementById('english-results-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            const modalContent = overlay.querySelector('.english-modal-content');
            if (modalContent) {
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'scale(0.95)';
            }
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 400);
        }
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert(message);
        }
    }

    /**
     * Add modal styles to the page
     */
    function addModalStyles() {
        // Check if styles already exist
        if (document.getElementById('english-results-modal-styles')) {
            return;
        }

        const styleSheet = document.createElement('style');
        styleSheet.id = 'english-results-modal-styles';
        styleSheet.textContent = `
            /* English Results Modal Styles - Matches Skills Gap Modal Theme */
            .english-modal-overlay {
                position: fixed;
                top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.4s ease;
                z-index: 1000;
            }

            .english-modal-content {
                background: #ffffff;
                border-radius: 6px;
                width: 80%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.4s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                font-size: 0.875rem;
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            .english-modal-header {
                padding: 0.75rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .english-modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .english-modal-employee-title {
                font-size: 1rem;
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .english-modal-subtitle {
                font-size: 0.8rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            .english-modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .english-close-modal-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .english-close-modal-button svg {
                stroke: #1e3a8a;
            }

            .english-close-modal-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            .english-modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            /* Score Overview Section */
            .english-score-overview {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin-bottom: 1.5rem;
                border-left: 4px solid #1e3a8a;
            }

            .english-score-overview.qualified {
                border-left-color: #059669;
            }

            .english-score-overview.needs-improvement {
                border-left-color: #f59e0b;
            }

            .english-score-display {
                text-align: center;
                margin-bottom: 1rem;
            }

            .english-score-value {
                font-size: 2rem;
                font-weight: 600;
                color: #1e3a8a;
                line-height: 1;
            }

            .english-score-value.overridden {
                color: #dc2626;
                position: relative;
            }

            .english-score-level.overridden {
                color: #dc2626;
            }

            .override-indicator {
                font-size: 0.75rem;
                color: #dc2626;
                font-weight: 500;
                background: #fef2f2;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                margin: 0.25rem 0;
                border: 1px solid #fecaca;
                display: inline-block;
            }

            .english-score-level {
                font-size: 0.9rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0.5rem 0;
            }

            .english-score-status {
                font-size: 0.8rem;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                display: inline-block;
                background: #f0f9ff;
                color: #1e3a8a;
            }

            .english-score-status.qualified {
                background: #f0fdf4;
                color: #059669;
            }

            .english-score-status.needs-improvement {
                background: #fffbeb;
                color: #f59e0b;
            }

            .english-assessment-meta {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 1rem;
                font-size: 0.8rem;
            }

            .english-meta-item {
                text-align: center;
            }

            .english-meta-label {
                display: block;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .english-meta-value {
                font-weight: 600;
                color: #374151;
            }

            /* Analysis Section */
            .english-analysis-section {
                margin-bottom: 1.5rem;
            }

            .english-analysis-section h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .english-feedback-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .english-feedback-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .english-feedback-item h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .english-feedback-item p {
                font-size: 0.8rem;
                color: #6b7280;
                margin: 0;
                line-height: 1.4;
            }

            .english-overall-feedback {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                border-left: 4px solid #1e3a8a;
            }

            .english-overall-feedback h4 {
                margin-top: 0;
                font-size: 0.9rem;
                color: #1e3a8a;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            .english-overall-feedback p {
                font-size: 0.8rem;
                color: #374151;
                margin: 0;
                line-height: 1.4;
            }

            /* Course Recommendations */
            .english-course-recommendations {
                background: #f0f9ff;
                border-radius: 8px;
                padding: 1.5rem;
                margin-bottom: 1.5rem;
                border: 1px solid #e0f2fe;
            }

            .english-course-recommendations h3 {
                margin-top: 0;
                margin-bottom: 1rem;
                font-weight: 600;
                font-size: 1rem;
                color: #0f172a;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .english-course-recommendations h3::before {
                content: "🎯";
                font-size: 1.2rem;
            }

            .english-recommendations-content {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .english-recommendations-description p {
                font-size: 0.85rem;
                color: #475569;
                margin: 0;
                line-height: 1.5;
                font-style: italic;
            }

            .english-eligible-courses h4,
            .english-next-steps-recommendation h4 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-weight: 600;
                font-size: 0.9rem;
                color: #1e293b;
            }

            .english-courses-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .english-course-item {
                background: #ffffff;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 0.75rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.2s ease;
            }

            .english-course-item:hover {
                border-color: #3b82f6;
                box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
            }

            .english-course-icon {
                font-size: 1rem;
                flex-shrink: 0;
            }

            .english-course-name {
                font-size: 0.8rem;
                color: #374151;
                font-weight: 500;
            }

            .english-next-steps-recommendation p {
                font-size: 0.8rem;
                color: #475569;
                margin: 0;
                line-height: 1.4;
                background: #ffffff;
                padding: 0.75rem;
                border-radius: 6px;
                border-left: 4px solid #3b82f6;
            }

            /* Strengths and Improvements */
            .english-strengths-improvements {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .english-strengths-section,
            .english-improvements-section {
                background: #f9fafb;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid transparent;
            }

            .english-strengths-section {
                border-left-color: #059669;
            }

            .english-improvements-section {
                border-left-color: #f59e0b;
            }

            .english-strengths-section h4,
            .english-improvements-section h4 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-weight: 600;
                font-size: 0.9rem;
                color: #374151;
            }

            .english-strengths-list,
            .english-improvements-list {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .english-strengths-list li,
            .english-improvements-list li {
                border-bottom: 1px solid #e5e7eb;
                padding: 0.5rem 0;
                font-size: 0.8rem;
                color: #374151;
            }

            .english-strengths-list li:last-child,
            .english-improvements-list li:last-child {
                border-bottom: none;
            }

            /* Next Steps */
            .english-next-steps {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                border-left: 4px solid #1e3a8a;
            }

            .english-next-steps h4 {
                margin-top: 0;
                font-size: 0.9rem;
                color: #1e3a8a;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            .english-next-steps-text {
                font-size: 0.8rem;
                color: #374151;
                margin: 0;
                line-height: 1.4;
            }

            /* Error State */
            .english-error-state {
                color: #6b7280;
                font-size: 0.85rem;
                text-align: center;
                padding: 2rem 1rem;
                font-style: italic;
            }

            .english-error-icon {
                font-size: 2rem;
                margin-bottom: 1rem;
            }

            .english-error-state h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #374151;
                margin-bottom: 0.5rem;
            }

            .english-error-state p {
                color: #6b7280;
                margin-bottom: 1rem;
                font-size: 0.8rem;
            }

            .english-retry-button {
                background: #1e3a8a;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                font-size: 0.8rem;
                transition: background-color 0.2s ease;
            }

            .english-retry-button:hover {
                background: #1e40af;
            }

            /* Admin Review Section */
            .english-admin-review {
                background: #f0f9ff;
                border: 1px solid #e0f2fe;
                border-radius: 8px;
                padding: 1.5rem;
                margin-top: 1.5rem;
            }

            .english-admin-review h4 {
                margin-top: 0;
                margin-bottom: 1rem;
                font-weight: 600;
                font-size: 1rem;
                color: #0f172a;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .english-admin-review h4::before {
                content: "🔧";
                font-size: 1.2rem;
            }

            .admin-review-buttons {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1rem;
                flex-wrap: wrap;
            }

            .admin-review-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1rem;
                border: none;
                border-radius: 6px;
                font-size: 0.9rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .admin-review-btn.primary {
                background: #3b82f6;
                color: white;
            }

            .admin-review-btn.primary:hover {
                background: #2563eb;
                transform: translateY(-1px);
            }

            .admin-review-btn.secondary {
                background: #6b7280;
                color: white;
            }

            .admin-review-btn.secondary:hover {
                background: #4b5563;
                transform: translateY(-1px);
            }

            .responses-available {
                color: #10b981;
                font-size: 0.85rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .responses-unavailable {
                color: #f59e0b;
                font-size: 0.85rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .admin-review-info {
                font-size: 0.85rem;
                color: #475569;
                line-height: 1.4;
            }

            .admin-review-info p {
                margin: 0;
            }
        `;
        document.head.appendChild(styleSheet);
    }

    /**
     * Get course recommendation summary across all users with completed English assessments
     * This function aggregates course recommendation data for analytics purposes
     * @param {string} companyId - Company ID (defaults to 'Birmingham')
     * @returns {Promise<Array>} Array of course recommendations with counts
     */
    async function getCourseRecommendationSummary(companyId = 'Birmingham') {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            const usersSnapshot = await db
                .collection('companies')
                .doc(companyId)
                .collection('users')
                .where('englishAssessmentCompleted', '==', true)
                .get();

            const courseCount = {};

            usersSnapshot.forEach(doc => {
                const userData = doc.data();
                const recommendations = userData.courseRecommendations?.eligible || [];
                recommendations.forEach(course => {
                    courseCount[course] = (courseCount[course] || 0) + 1;
                });
            });

            return Object.entries(courseCount)
                .map(([course, count]) => ({ course, count }))
                .sort((a, b) => b.count - a.count);

        } catch (error) {
            console.error('Error getting course recommendation summary:', error);
            throw error;
        }
    }

    /**
     * Fetch comprehensive English assessment report for a specific user
     * Returns data in format compatible with React components
     * @param {string} userEmail - User's email address
     * @param {string} companyId - Company ID (defaults to 'Birmingham')
     * @returns {Promise<Object>} Comprehensive English assessment report
     */
    async function fetchUserEnglishReport(userEmail, companyId = 'Birmingham') {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            const userDoc = await db
                .collection('companies')
                .doc(companyId)
                .collection('users')
                .doc(userEmail)
                .get();

            if (!userDoc.exists) {
                throw new Error('User not found');
            }

            const userData = userDoc.data();

            // Extract English assessment data with comprehensive structure
            const englishReport = {
                userEmail: userEmail,
                completed: userData.englishAssessmentCompleted || false,
                score: userData.englishProficiencyScore || 0,
                level: userData.englishProficiencyLevel || 'Entry Level',
                feedback: userData.englishFeedback || {},
                strengths: userData.englishStrengths || [],
                improvements: userData.englishImprovements || [],
                courseRecommendations: userData.courseRecommendations || {},
                completedAt: userData.englishAssessmentTimestamp || userData.updatedAt || null,

                // Additional user context
                name: userData.name || `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown',
                studentLevel: userData.studentLevel || userData.userRole || 'Not specified',

                // Legacy compatibility
                totalPoints: userData.englishProficiencyScore || 0,
                timestamp: userData.englishAssessmentTimestamp || userData.updatedAt || null
            };

            return englishReport;

        } catch (error) {
            console.error('Error fetching English report:', error);
            throw error;
        }
    }

    // Expose functions globally for use in other modules
    window.getCourseRecommendationSummary = getCourseRecommendationSummary;
    window.fetchUserEnglishReport = fetchUserEnglishReport;

})();
